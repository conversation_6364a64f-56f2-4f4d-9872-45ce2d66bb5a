###################### BASE STAGE ########################## 
FROM nexus.tmbbank.local:60014/docker-agent/docker-agent-java17:latest AS base
ARG TZ=Asia/Bangkok
ENV TZ=$TZ
WORKDIR /app/jboss
RUN cp /app/gradle.keystore gradle.keystore
COPY . .
USER 0 
RUN chown -R jboss:jboss /app
USER jboss

###################### BUILD STAGE ##########################
FROM base AS build
WORKDIR /app/jboss
ARG NEXUS_USERNAME
ARG NEXUS_PASSWORD
RUN /app/.gradle/gradle-8.5/bin/gradle \
    -PrepoUsername=$NEXUS_USERNAME \
    -PrepoPassword=$NEXUS_PASSWORD \
    -x test \
    clean build --refresh-dependencies

###################### SCAN STAGE ##########################
FROM base AS sonar-scan
WORKDIR /app/jboss
ARG SONAR_SCAN
ARG SONAR_PROJECT_KEY
ARG SONAR_PROJECT_VERSION
ARG NEXUS_USERNAME
ARG NEXUS_PASSWORD
ARG SONAR_HOST_URL
ARG SONAR_TOKEN

RUN /app/.gradle/gradle-8.5/bin/gradle \
    clean test sonarqube \
    -PrepoUsername=$NEXUS_USERNAME \
    -PrepoPassword=$NEXUS_PASSWORD \
    -PprojVersion=$SONAR_PROJECT_VERSION  \
    -Dsonar.host.url=$SONAR_HOST_URL \
    -Dsonar.projectKey=$SONAR_PROJECT_KEY \
    -Dsonar.projectName=$SONAR_PROJECT_KEY \
    -Dsonar.projectVersion=$SONAR_PROJECT_VERSION \
    -Dsonar.login=$SONAR_TOKEN \
    --refresh-dependencies


###################### EXPORT TASK ID STAGE ##########################
FROM scratch AS scan
COPY --from=sonar-scan /app/jboss/build/sonar/report-task.txt /report-task.txt

###################### PRODUCTION STAGE ##########################
FROM nexus.tmbbank.local:60021/openjdk-17-runtime:1.23-3.********** AS production
USER 0
RUN update-crypto-policies --set LEGACY
WORKDIR /home/<USER>
USER jboss
COPY --from=build /app/jboss/build/libs/*.jar /app/app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-Duser.timezone=GMT+7", "-Djava.security.egd=file:/dev/./urandom", "-jar", "/app/app.jar"]