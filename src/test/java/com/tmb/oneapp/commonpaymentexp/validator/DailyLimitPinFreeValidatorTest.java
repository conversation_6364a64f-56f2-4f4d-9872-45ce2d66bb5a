package com.tmb.oneapp.commonpaymentexp.validator;

import com.tmb.common.model.CommonData;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CommonServiceClient;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthForceFR;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.client.CommonAuthenticationService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_PRE_LOGIN;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DailyLimitPinFreeValidatorTest {
    @InjectMocks
    DailyLimitPinFreeValidator dailyLimitPinFreeValidator;

    @Mock
    CustomerServiceClient customerServiceClient;
    @Mock
    CommonServiceClient commonServiceClient;
    @Mock
    CommonAuthenticationService commonAuthenticationService;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    CustomerCrmProfile customerCrmProfile;
    BigDecimal amountBigDecimal;
    boolean isPreLogin;
    boolean isOwner;
    Integer limit;
    Integer count;
    String settingFlag;
    CommonData commonData;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(dailyLimitPinFreeValidator, "totalPaymentAccumulateUsageLimit", new BigDecimal("200000"));

        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);

        amountBigDecimal = BigDecimal.valueOf(500.50);
        isPreLogin = false;
        isOwner = false;

        customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setBillpayMaxLimitAmt(1000);
        customerCrmProfile.setBillpayAccuUsgAmt(BigDecimal.valueOf(200.00));
        customerCrmProfile.setPinFreeBpLimit(200);
        customerCrmProfile.setPinFreeTxnCount(1);
        customerCrmProfile.setPinFreeSeetingFlag("Y");

        customerCrmProfile.setEbMaxLimitAmtCurrent(1000);
        customerCrmProfile.setEbAccuUsgAmtDaily(200.00);
        customerCrmProfile.setPinFreeTrLimit(200);

        commonData = new CommonData();
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenPreLoginThenRequireCommonAuthen() {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_PRE_LOGIN, "true");
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);

        BigDecimal reqAmount = new BigDecimal("5000.00");
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CommonAuthenResult result = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerCrmProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(result.isRequireCommonAuthen());
        assertNull(result.getIsDDP());
        assertFalse(result.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenIsOwnThenNotRequireCommonAuthen() {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("5000.00");
        boolean isOwn = true;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CommonAuthenResult actual = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerCrmProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertFalse(actual.isRequireCommonAuthen());
        assertNull(actual.getIsDDP());
        assertFalse(actual.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenPinFreeDisabledThenRequireCommonAuthen() {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("500.00");
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("N");

        CommonAuthenResult actual = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(actual.isRequireCommonAuthen());
        assertNull(actual.getIsDDP());
        assertFalse(actual.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenAmountMoreThanLimitThenRequireCommonAuthen() {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("2000.00");
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");

        CommonAuthenResult actual = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(actual.isRequireCommonAuthen());
        assertNull(actual.getIsDDP());
        assertFalse(actual.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenNotPinFreeThenRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("500.00");
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(20);

        commonData.setPinFreeMaxTrans("15");
        mockFetchCommonConfig(commonData);

        CommonAuthenResult actual = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(actual.isRequireCommonAuthen());
        assertNull(actual.getIsDDP());
        assertFalse(actual.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenInDDPThenRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("500.00");
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);

        commonData.setPinFreeMaxTrans("15");
        mockFetchCommonConfig(commonData);
        CommonAuthForceFR forceFR = new CommonAuthForceFR();
        forceFR.setIsForce(true);
        forceFR.setCrmId(crmId);
        when(commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(forceFR);

        CommonAuthenResult actual = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(actual.isRequireCommonAuthen());
        assertTrue(actual.getIsDDP());
        assertTrue(actual.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenNotInDDPAndWithinLimitThenNotRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("500.00");
        boolean isOwn = false;
        double pinFreeTranLimit = 1000.00;
        boolean paymentAccumulateUsageLimitRule = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal(1000));

        commonData.setPinFreeMaxTrans("15");
        mockFetchCommonConfig(commonData);
        when(commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(null);

        CommonAuthenResult actual = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertFalse(actual.isRequireCommonAuthen());
        assertFalse(actual.getIsDDP());
        assertTrue(actual.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenNotInDDPButExceedPaymentAccumulateLimitThenRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("1000.00");
        boolean isOwn = false;
        double pinFreeTranLimit = 2000.00;
        boolean paymentAccumulateUsageLimitRule = true;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal("199500"));

        commonData.setPinFreeMaxTrans("15");
        mockFetchCommonConfig(commonData);
        when(commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(null);

        CommonAuthenResult actual = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertTrue(actual.isRequireCommonAuthen());
        assertFalse(actual.getIsDDP());
        assertTrue(actual.isPinFree());
    }

    @Test
    void testValidateIsRequireCommonAuthenWhenNotInDDPAndWithinAccumulateLimitThenNotRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("1000.00");
        boolean isOwn = false;
        double pinFreeTranLimit = 2000.00;
        boolean paymentAccumulateUsageLimitRule = true;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal("190000"));

        commonData.setPinFreeMaxTrans("15");
        mockFetchCommonConfig(commonData);
        when(commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(null);

        CommonAuthenResult actual = ReflectionTestUtils.invokeMethod(
                dailyLimitPinFreeValidator, "validateIsRequireCommonAuth", testHeaders, reqAmount, isOwn, customerProfile, pinFreeTranLimit, paymentAccumulateUsageLimitRule);

        assertFalse(actual.isRequireCommonAuthen());
        assertFalse(actual.getIsDDP());
        assertTrue(actual.isPinFree());
    }


    @Test
    void testValidateIsRequireCommonAuthForTopUpWhenWithinLimitsShouldNotRequireCommonAuthen() throws Exception {
        HttpHeaders testHeaders = new HttpHeaders();
        testHeaders.add(HEADER_CRM_ID, crmId);
        testHeaders.add(HEADER_CORRELATION_ID, correlationId);
        testHeaders.add(HEADER_PRE_LOGIN, "false");

        BigDecimal reqAmount = new BigDecimal("100.00");
        boolean isOwn = false;

        CustomerCrmProfile customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(5);
        customerProfile.setPinFreeBpLimit(200);
        customerProfile.setPinFreeTrLimit(100);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal("190000"));

        commonData.setPinFreeMaxTrans("15");
        mockFetchCommonConfig(commonData);
        when(commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId)).thenReturn(null);

        CommonAuthenResult actual = dailyLimitPinFreeValidator.validateIsRequireCommonAuthForTopUp(testHeaders, reqAmount, isOwn, customerProfile);

        assertFalse(actual.isRequireCommonAuthen());
        assertFalse(actual.getIsDDP());
        assertTrue(actual.isPinFree());
    }
    private void mockFetchCommonConfig(CommonData commonData) {
        TmbServiceResponse<List<CommonData>> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(List.of(commonData));
        when(commonServiceClient.fetchCommonConfig("common_module", correlationId)).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockFetchCustomerCrmProfile() {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeBpLimit(limit);
        customerCrmProfile.setPinFreeSeetingFlag(settingFlag);
        customerCrmProfile.setPinFreeTxnCount(count);

        TmbServiceResponse<CustomerCrmProfile> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(customerCrmProfile);
        when(customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId)).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }
}