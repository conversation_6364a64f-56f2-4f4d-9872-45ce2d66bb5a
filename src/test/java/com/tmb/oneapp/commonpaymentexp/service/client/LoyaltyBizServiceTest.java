package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class LoyaltyBizServiceTest {

    @Mock
    private FeignClientHelper feignClientHelper;

    @InjectMocks
    private  LoyaltyBizService loyaltyBizService;

    @Test
    void redeemPointSuccessTest() throws TMBCommonException {
        loyaltyBizService.redeemPoint(new HttpHeaders() , new WowPointRedeemConfirmRequest());

        verify(feignClientHelper).executeVoidRequest(any());
    }

    @Test
    void redeemPointFailedWhenFeignExceptionTest() throws TMBCommonException {
        TMBCommonException expectedException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                ResponseCode.FAILED_V2.getMessage(),
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null);

        doThrow(expectedException).when(feignClientHelper).executeVoidRequest(any());

        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () -> loyaltyBizService.redeemPoint(new HttpHeaders(), new WowPointRedeemConfirmRequest()));
        Assertions.assertEquals(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(),actual.getErrorCode());
    }
}