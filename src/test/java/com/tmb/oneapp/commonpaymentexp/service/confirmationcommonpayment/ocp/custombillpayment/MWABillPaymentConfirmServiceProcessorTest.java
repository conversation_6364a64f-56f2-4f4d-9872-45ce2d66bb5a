package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ocp.custombillpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccountPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBalance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialCustomBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialMWABillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MWA;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MWABillPaymentConfirmServiceProcessorTest {
    @InjectMocks
    private MWABillPaymentConfirmServiceProcessor mwaBillPaymentConfirmServiceProcessor;

    @Mock
    private BaseConfirmServiceHelper baseConfirmServiceHelper;
    @Mock
    private CommonValidateConfirmationService commonValidateConfirmationService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private LoyaltyBizService loyaltyBizService;

    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile customerProfile;
    private OCPBillPaymentResponse ocpBillPaymentResponse;
    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private ConfirmationCommonPaymentRequest request;
    private BasePrepareDataConfirm basePrepareDataConfirm;

    @BeforeEach
    void setUp() {
        TestUtils.setUpAsyncExecuteMethodAsyncSafelyVoid(asyncHelper);

        String transactionId = "txn-001";
        crmId = "crm-001";
        correlationId = "corr-001";
        String ipAddress = "127.0.0.1";

        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.set(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, "en");
        headers.set(CommonPaymentExpConstant.HEADER_APP_VERSION, "5.12.0");

        request = new ConfirmationCommonPaymentRequest();
        request.setTransactionId(transactionId);

        basePrepareDataConfirm = new BasePrepareDataConfirm();

        customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(0);
        customerProfile.setAutoSaveSlipMain("Y");

        draftCache = createDraftCache();

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        OCPAccount account = new OCPAccount();
        account.setAvailBal("5000.00");
        ocpBillPayment.setAccount(account);
        OCPAccountPayment fromAccount = new OCPAccountPayment();
        fromAccount.setAccountId("**********");
        fromAccount.setAccountType("SDA");
        ocpBillPayment.setFromAccount(fromAccount);
        ocpBillPayment.setRef4("100.00");

        ocpBillPaymentResponse = new OCPBillPaymentResponse();
        ocpBillPaymentResponse.setData(ocpBillPayment);
    }

    @Test
    void testGetProcessorTypeWhenCalledThenReturnMWABillPayment() {
        String result = mwaBillPaymentConfirmServiceProcessor.getProcessorType();
        assertEquals(BILLER_PAYMENT_MWA, result);
    }

    @Test
    void testCreateFinancialLogWhenCalledThenReturnFinancialMWABillPayActivityLog() {
        draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().setEpayCode("epay-001");
        FinancialCustomBillPayActivityLog result = mwaBillPaymentConfirmServiceProcessor.initialFinancialLog(request,
                headers,
                draftCache
        );

        assertNotNull(result);
        assertEquals(crmId, result.getCrmId());
        assertEquals("epay-001", result.getReferenceID());
        assertEquals(correlationId, result.getActivityRefId());

        assertEquals("UI001", result.getFinFlexValues1());
        assertEquals("********", result.getFinFlexValues2());
        assertNull(result.getFinFlexValues3());
        assertEquals("800.00|500", result.getFinFlexValues4());
    }

    @Test
    void testCreateFinancialLogWhenMissingAdditionalParamsThenSetDefaultValues() {
        draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().setEpayCode("epay-001");
        draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().setAdditionalParams(new ArrayList<>());

        FinancialCustomBillPayActivityLog result = mwaBillPaymentConfirmServiceProcessor.initialFinancialLog(request,
                headers,
                draftCache
        );

        assertNotNull(result);
        assertEquals("", result.getFinFlexValues1());
        assertNull(result.getFinFlexValues2());
        assertNull(result.getFinFlexValues3());
        assertEquals("800.00|0.00", result.getFinFlexValues4());
    }

    @Test
    void testCreateFinancialLogWhenEmptyMessageThenNoBillInfo() {
        List<AdditionalParam> partialParams = new ArrayList<>();
        partialParams.add(new AdditionalParam("UI", "Jane Doe"));
        partialParams.add(new AdditionalParam("TotalInterest", "300"));
        partialParams.add(new AdditionalParam("Message", ""));
        draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().setAdditionalParams(partialParams);
        draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().setEpayCode("epay-001");

        FinancialCustomBillPayActivityLog result = mwaBillPaymentConfirmServiceProcessor.initialFinancialLog(request,
                headers,
                draftCache
        );

        assertNotNull(result);
        assertEquals("Jane Doe", result.getFinFlexValues1());
        assertNull(result.getFinFlexValues2());
        assertNull(result.getFinFlexValues3());
        assertEquals("800.00|300", result.getFinFlexValues4());
    }

    @Test
    void testConfirmWhenSuccessThenReturnResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        mockGetBasePrepareDataConfirm(customerProfile);
        when(paymentService.confirmOCPBillPayment(any(), any(), any() ,any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = mwaBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals(new BigDecimal("5000.00"), response.getRemainingBalance());
        verify(commonValidateConfirmationService, times(1)).baseValidateData(eq(request), eq(headers), eq(draftCache), any(BasePrepareDataConfirm.class));
        verify(baseConfirmServiceHelper, times(1)).baseConfirmDataAfterConfirmExternal(request, headers, draftCache, basePrepareDataConfirm);
        verify(logEventPublisherService, times(1)).saveActivityLog(any(), any());
        verify(logEventPublisherService, times(1)).saveFinancialLog(eq(correlationId), any(FinancialMWABillPayActivityLog.class));
        verify(logEventPublisherService, times(1)).saveTransactionLog(eq(correlationId), any());
    }

    private void mockGetBasePrepareDataConfirm(CustomerCrmProfile customerProfile) throws TMBCommonException {
        basePrepareDataConfirm.setCustomerCrmProfile(customerProfile);
        basePrepareDataConfirm.setTransactionTime(String.valueOf(System.currentTimeMillis()));
        when(baseConfirmServiceHelper.getBasePrepareDataConfirm(any(), anyString())).thenReturn(basePrepareDataConfirm);
    }

    @Test
    void testConfirmWhenPaymentServiceFailsThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        TMBCommonException throwException = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
        mockGetBasePrepareDataConfirm(customerProfile);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenThrow(throwException);
        when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenThrow(throwException);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                mwaBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        assertEquals(throwException.getErrorCode(), exception.getErrorCode());
    }

    @Nested
    class ConfirmWowTransactionInMEAProcessor {

        @Test
        void testConfirmWowWhenSuccessThenReturnResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirm(customerProfile);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);

            WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
            wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
            wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));

            draftCache.setCommonPaymentRule(commonPaymentRule);
            draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
            draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);

            Mockito.doNothing().when(loyaltyBizService).redeemPoint(any(), any());
            when(paymentService.confirmOCPBillWowPointPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

            ConfirmationCommonPaymentResponse response = mwaBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

            assertNotNull(response);
            assertEquals(new BigDecimal("5000.00"), response.getRemainingBalance());
            verify(commonValidateConfirmationService, times(1)).baseValidateData(eq(request), eq(headers), eq(draftCache), any(BasePrepareDataConfirm.class));
            verify(baseConfirmServiceHelper, times(1)).baseConfirmDataAfterConfirmExternal(request, headers, draftCache, basePrepareDataConfirm);
            verify(logEventPublisherService, times(1)).saveActivityLog(any(), any());
            verify(logEventPublisherService, times(1)).saveFinancialLog(eq(correlationId), any(FinancialMWABillPayActivityLog.class));
            verify(logEventPublisherService, times(1)).saveTransactionLog(eq(correlationId), any());
        }

        @Test
        void testConfirmWowWhenPaymentServiceFailsThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            TMBCommonException throwException = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
            mockGetBasePrepareDataConfirm(customerProfile);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);

            WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
            wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
            wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));

            draftCache.setCommonPaymentRule(commonPaymentRule);
            draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
            draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);
            Mockito.doThrow(throwException).when(loyaltyBizService).redeemPoint(any(), any());
            when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    mwaBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(throwException.getErrorCode(), exception.getErrorCode());
        }
    }

    private CommonPaymentDraftCache createDraftCache() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        OCPBillRequest ocpBillRequest = new OCPBillRequest();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        PaymentInformation paymentInformation = new PaymentInformation();
        ProductDetail productDetail = new ProductDetail();
        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();

        ocpBillRequest.setAmount("1000.00");
        ocpBillRequest.setEpayCode("epay-001");
        ocpBillRequest.setFee(new OCPFee());
        ocpBillRequest.setFromAccount(new OCPAccountPayment().setAccountId("43210"));
        ocpBillRequest.setCompCode("5006");
        ocpBillRequest.setToAccount(new OCPAccountPayment().setAccountId("01234"));
        ocpBillRequest.setRef2("200.00");

        OCPBalance balance = new OCPBalance();
        balance.setMax("1000.00");
        ocpBillRequest.setBalance(balance);

        List<AdditionalParam> additionalParams = new ArrayList<>();
        additionalParams.add(new AdditionalParam("TotalInterest", "500"));
        additionalParams.add(new AdditionalParam("UI", "UI001"));
        additionalParams.add(new AdditionalParam("Message", "1|202301|********|100.00|900.00"));
        ocpBillRequest.setAdditionalParams(additionalParams);

        billerInfo.setBillerGroupType("0");
        billerInfo.setPaymentMethod("5");
        billerInfo.setNameEn("MWA");
        billerInfo.setBillerCompCode("5006");

        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(new ReferenceResponse());

        validateRequest.setNote("test note");
        validateRequest.setCreditCard(new CreditCardValidationCommonPaymentRequest());
        validateRequest.setDeposit(new DepositValidationCommonPaymentRequest());

        productDetail.setProductRef1("ref1-001");

        paymentInformation.setCompCode("5006");
        paymentInformation.setProductDetail(productDetail);

        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setOcpBillPaymentConfirmRequest(ocpBillRequest));
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.setRequireCommonAuthen(false);
        validateDraftCache.setFromDepositAccount(new DepositAccountInCache());
        validateDraftCache.setFeeCalculated(new BigDecimal("10.50"));

        cache.setValidateDraftCache(validateDraftCache);
        cache.setValidateRequest(validateRequest);
        cache.setPaymentInformation(paymentInformation);
        cache.setCommonPaymentConfig(new CommonPaymentConfig());

        return cache;
    }
}