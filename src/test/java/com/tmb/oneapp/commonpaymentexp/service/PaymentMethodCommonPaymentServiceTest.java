package com.tmb.oneapp.commonpaymentexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.client.PaymentServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.AccountSaving;
import com.tmb.oneapp.commonpaymentexp.model.ConversionRateDetail;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.WowPoint;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentRule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ReviewScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.Schedule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CommonPaymentMethodPrepareDataTemp;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CreditCardPoint;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.PaymentMethodCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.ProcessBillPayDataTemp;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerDetailResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerSearchRequest;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.DStatementAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.IssueDebitCardAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_CONTACT_ADDRESS_TYPE;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_REGISTER_ADDRESS_TYPE;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.DEFAULT_PAYMENT_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.DEFAULT_PAYMENT_DEPOSIT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentMethodCommonPaymentServiceTest {
    @InjectMocks
    PaymentMethodCommonPaymentService paymentMethodCommonPaymentService;

    @Mock
    AsyncHelper asyncHelper;
    @Mock
    CacheService cacheService;
    @Mock
    PaymentServiceClient paymentServiceClient;
    @Mock
    CustomerServiceClient customerServiceClient;
    @Mock
    DStatementAccountCommonPaymentService dStatementAccountCommonPaymentService;
    @Mock
    IssueDebitCardAccountCommonPaymentService issueDebitCardAccountCommonPaymentService;
    @Mock
    BillPayPaymentMethodCommonPaymentHelper billPayPaymentMethodCommonPaymentHelper;

    HttpHeaders headers;
    String crmId;
    String correlationId;
    String transactionId;
    CommonPaymentDraftCache cache;
    CommonPaymentRule commonPaymentRule;
    MasterBillerResponse masterBiller;
    List<DepositAccount> depositAccountList;
    List<CreditCardSupplementary> creditCardAccountList;
    PaymentInformation paymentInformation;
    CustomerDetailResponse customerDetailResponse;
    String transactionType;
    String defaultPayment;
    CommonPaymentMethodPrepareDataTemp prepareData;
    WowPoint wowPoint;

    @BeforeEach
    void setup() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        transactionId = "transactionId";
        cache = new CommonPaymentDraftCache();
        commonPaymentRule = new CommonPaymentRule();
        depositAccountList = new ArrayList<>();
        creditCardAccountList = new ArrayList<>();
        paymentInformation = new PaymentInformation();
        masterBiller = new MasterBillerResponse();
        customerDetailResponse = new CustomerDetailResponse();

        transactionId = "transactionId";
        cache = new CommonPaymentDraftCache();
        prepareData = new CommonPaymentMethodPrepareDataTemp();
        wowPoint = new WowPoint();
        wowPoint.setMinPaymentAmount(new BigDecimal(100));
        wowPoint.setMin(new BigDecimal(10));
        wowPoint.setMax(new BigDecimal(10000));


        ConversionRateDetail conversionRateDetail = new ConversionRateDetail();
        conversionRateDetail.setNum(new BigDecimal(1));
        conversionRateDetail.setDen(new BigDecimal(10));

        wowPoint.setConversionRate(conversionRateDetail);
    }


    @Nested
    class BillTest {
        @Test
        void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultDepositPayment_ShouldReturnDepositAccountSuccess() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
            defaultPayment = DEFAULT_PAYMENT_DEPOSIT;
            paymentInformation.setTransactionType(transactionType);
            commonPaymentRule.setDefaultPayment(defaultPayment);
            setUpAsyncHelper();
            mockGetCacheReturnSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeAndCodeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnSuccess(masterBiller);
            mockFetchCustomerKYCReturnSuccess(customerDetailResponse);
            mockProcessBillPayReturnDepositAccountListSuccess(depositAccountList);

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertTrue(actual.isDepositAccountFlag());
            assertObjectAreEqual(actual.getDepositAccountList(), depositAccountList);
            assertObjectAreEqual(actual.getWowPoint(), commonPaymentRule.getWowPoint());

            //assertFalse
            Assertions.assertFalse(actual.isCreditCardFlag());
            //assertTrue
            Assertions.assertTrue(actual.isCreditCardPointFlag());
            Assertions.assertTrue(actual.isCreditCardOtherFlag());
            Assertions.assertTrue(actual.isQrCodeFlag());
            Assertions.assertTrue(actual.isCouponFlag());
            Assertions.assertTrue(actual.isCreditCardInstallmentFlag());
            Assertions.assertTrue(actual.isWowPointFlag());

            Assertions.assertEquals(COMMON_PAYMENT_CONTACT_ADDRESS_TYPE, actual.getAddress().getAddressType());
            Assertions.assertEquals(customerDetailResponse.getContactAddress(), actual.getAddress().getContactAddress());
            Assertions.assertEquals(customerDetailResponse.getCustomerNameEn(), actual.getAddress().getCustomerNameEn());
            Assertions.assertEquals(customerDetailResponse.getCustomerNameTh(), actual.getAddress().getCustomerNameTh());

            assertObjectAreEqual(paymentInformation, actual.getPaymentInformation());
            assertObjectAreEqual(masterBiller, actual.getMasterBillerResponse());
            Assertions.assertEquals(DEFAULT_PAYMENT_DEPOSIT, actual.getDefaultPaymentOverride());
            assertObjectAreEqual(paymentInformation.getReviewScreenDetail(), actual.getPaymentInformation().getReviewScreenDetail());
        }

        @Test
        void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultCreditCardPayment_ShouldReturnCreditCardAccountSuccess() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
            defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
            paymentInformation.setTransactionType(transactionType);
            commonPaymentRule.setDefaultPayment(defaultPayment);
            setUpAsyncHelper();
            mockGetCacheReturnSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeAndCodeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnCreditCardBillerSuccess(masterBiller);
            mockFetchCustomerKYCReturnOnlyRegisterAddressSuccess(customerDetailResponse);
            mockProcessBillPayReturnCreditCardAccountListSuccess(creditCardAccountList);

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertTrue(actual.isCreditCardFlag());
            assertObjectAreEqual(actual.getCreditCardList(), CacheMapper.INSTANCE.toListCreditCardSupplementaryInCache(creditCardAccountList));

            Assertions.assertTrue(actual.isCreditCardPointFlag());
            assertMappingCreditCardPointCorrectly(creditCardAccountList, actual);
            Assertions.assertTrue(actual.isDepositAccountFlag());
            assertObjectAreEqual(actual.getWowPoint(), commonPaymentRule.getWowPoint());

            //assertTrue
            Assertions.assertTrue(actual.isCreditCardOtherFlag());
            Assertions.assertTrue(actual.isQrCodeFlag());
            Assertions.assertTrue(actual.isCouponFlag());
            Assertions.assertTrue(actual.isCreditCardInstallmentFlag());
            Assertions.assertTrue(actual.isWowPointFlag());

            Assertions.assertEquals(COMMON_PAYMENT_REGISTER_ADDRESS_TYPE, actual.getAddress().getAddressType());
            Assertions.assertEquals(customerDetailResponse.getRegisterAddress(), actual.getAddress().getContactAddress());
            Assertions.assertEquals(customerDetailResponse.getCustomerNameEn(), actual.getAddress().getCustomerNameEn());
            Assertions.assertEquals(customerDetailResponse.getCustomerNameTh(), actual.getAddress().getCustomerNameTh());

            assertObjectAreEqual(paymentInformation, actual.getPaymentInformation());
            assertObjectAreEqual(masterBiller, actual.getMasterBillerResponse());
            Assertions.assertEquals(DEFAULT_PAYMENT_CREDIT_CARD, actual.getDefaultPaymentOverride());
        }

        @Test
        void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultCreditCardPaymentButGetCreditCardAccountException_ShouldReturnDepositAccountSuccess() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
            defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
            paymentInformation.setTransactionType(transactionType);
            commonPaymentRule.setDefaultPayment(defaultPayment);
            setUpAsyncHelper();
            mockGetCacheReturnSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeAndCodeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnCreditCardBillerSuccess(masterBiller);
            mockFetchCustomerKYCReturnOnlyRegisterAddressSuccess(customerDetailResponse);
            mockProcessBillPayReturnIsCallCreditCardErrorAndDepositAccountListSuccess(creditCardAccountList);

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertTrue(actual.isCreditCardFlag());
            Assertions.assertNull(actual.getCreditCardList());
            Assertions.assertTrue(actual.isDepositAccountFlag());
            assertObjectAreEqual(actual.getDepositAccountList(), creditCardAccountList);
            assertObjectAreEqual(actual.getWowPoint(), commonPaymentRule.getWowPoint());

            Assertions.assertTrue(actual.isCreditCardPointFlag());
            Assertions.assertNull(actual.getCreditCardPoint());

            //assertTrue
            Assertions.assertTrue(actual.isCreditCardOtherFlag());
            Assertions.assertTrue(actual.isQrCodeFlag());
            Assertions.assertTrue(actual.isCouponFlag());
            Assertions.assertTrue(actual.isCreditCardInstallmentFlag());
            Assertions.assertTrue(actual.isWowPointFlag());

            Assertions.assertEquals(COMMON_PAYMENT_REGISTER_ADDRESS_TYPE, actual.getAddress().getAddressType());
            Assertions.assertEquals(customerDetailResponse.getRegisterAddress(), actual.getAddress().getContactAddress());
            Assertions.assertEquals(customerDetailResponse.getCustomerNameEn(), actual.getAddress().getCustomerNameEn());
            Assertions.assertEquals(customerDetailResponse.getCustomerNameTh(), actual.getAddress().getCustomerNameTh());

            assertObjectAreEqual(paymentInformation, actual.getPaymentInformation());
            assertObjectAreEqual(masterBiller, actual.getMasterBillerResponse());
            Assertions.assertEquals(DEFAULT_PAYMENT_DEPOSIT, actual.getDefaultPaymentOverride());
        }

        @Test
        void testGetCommonPaymentMethod_WhenTransactionBillPayGotNoEligibleAccount_ShouldDoesNotThrow() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
            defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
            paymentInformation.setTransactionType(transactionType);
            commonPaymentRule.setDefaultPayment(defaultPayment);
            setUpAsyncHelper();
            mockGetCacheReturnSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeAndCodeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnCreditCardBillerSuccess(masterBiller);
            mockFetchCustomerKYCReturnSuccess(customerDetailResponse);
            mockProcessBillPayReturnNoEligibleAccount(depositAccountList, creditCardAccountList);

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertTrue(actual.isDepositAccountFlag());
            Assertions.assertNull(actual.getDepositAccountList());
            Assertions.assertTrue(actual.isCreditCardFlag());
            Assertions.assertNull(actual.getCreditCardList());

            assertObjectAreEqual(paymentInformation, actual.getPaymentInformation());
            assertObjectAreEqual(masterBiller, actual.getMasterBillerResponse());
            Assertions.assertNull(actual.getDefaultPaymentOverride());
        }

        @Test
        void testGetCommonPaymentMethod_WhenTransactionBillPayMissingConfigurationException_ShouldThrowTmbCommonException() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
            defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
            paymentInformation.setTransactionType(transactionType);
            commonPaymentRule.setDefaultPayment(defaultPayment);
            setUpAsyncHelper();
            mockGetCacheReturnSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeAndCodeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnSuccess(masterBiller);
            mockFetchCustomerKYCReturnSuccess(customerDetailResponse);
            mockProcessBillPayThrowMissingConfigurationError();

            Assertions.assertThrows(TMBCommonException.class, () -> paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers));
        }
    }

    @Nested
    class IssueDebitCardTest {
        @Test
        void testGetCommonPaymentMethod_WhenTransactionIssueDebitCard_ShouldReturnDStatementAccountSuccess() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD;
            paymentInformation.setTransactionType(transactionType);
            setUpAsyncHelper();
            mockGetCacheReturnPaymentDetailWithoutCodeSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnSuccess(masterBiller);
            mockFetchCustomerKYCReturnSuccess(customerDetailResponse);
            mockGetAccountIssueDebitCardReturnIssueDebitCardAccountListSuccess(depositAccountList);

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertTrue(actual.isDepositAccountFlag());
            assertObjectAreEqual(actual.getDepositAccountList(), CacheMapper.INSTANCE.toListDepositAccountInCache(depositAccountList));
            assertObjectAreEqual(paymentInformation, actual.getPaymentInformation());
            assertObjectAreEqual(masterBiller, actual.getMasterBillerResponse());
            Assertions.assertEquals(DEFAULT_PAYMENT_DEPOSIT, actual.getDefaultPaymentOverride());
        }

        @Test
        void testGetCommonPaymentMethod_WhenTransactionIssueDebitCardFeignException_ShouldDoesNotThrow() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD;
            paymentInformation.setTransactionType(transactionType);
            setUpAsyncHelper();
            mockGetCacheReturnPaymentDetailWithoutCodeSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnSuccess(masterBiller);
            mockFetchCustomerKYCReturnSuccess(customerDetailResponse);
            mockGetAccountIssueDebitCardThrowNoEligibleAccountError();

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertTrue(actual.isDepositAccountFlag());
            Assertions.assertNull(actual.getDepositAccountList());
            assertObjectAreEqual(paymentInformation, actual.getPaymentInformation());
            assertObjectAreEqual(masterBiller, actual.getMasterBillerResponse());
            Assertions.assertNull(actual.getDefaultPaymentOverride());
        }
    }

    @Nested
    class DStatementTest {
        @Test
        void testGetCommonPaymentMethod_WhenTransactionDStatement_ShouldReturnDStatementAccountSuccess() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT;
            paymentInformation.setTransactionType(transactionType);
            setUpAsyncHelper();
            mockGetCacheReturnPaymentDetailWithoutCodeSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnSuccess(masterBiller);
            mockFetchCustomerKYCReturnSuccess(customerDetailResponse);
            mockGetAccountDStatementReturnDStatementAccountListSuccess(depositAccountList);

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertTrue(actual.isDepositAccountFlag());
            assertObjectAreEqual(actual.getDepositAccountList(), CacheMapper.INSTANCE.toListDepositAccountInCache(depositAccountList));
            assertObjectAreEqual(paymentInformation, actual.getPaymentInformation());
            assertObjectAreEqual(masterBiller, actual.getMasterBillerResponse());
            Assertions.assertEquals(DEFAULT_PAYMENT_DEPOSIT, actual.getDefaultPaymentOverride());
        }

        @Test
        void testGetCommonPaymentMethod_WhenTransactionDStatementFeignException_ShouldDoesNotThrow() throws TMBCommonException {
            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT;
            paymentInformation.setTransactionType(transactionType);
            setUpAsyncHelper();
            mockGetCacheReturnPaymentDetailWithoutCodeSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnSuccess(masterBiller);
            mockFetchCustomerKYCReturnSuccess(customerDetailResponse);
            mockGetAccountDStatementThrowNoEligibleAccountError();

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertTrue(actual.isDepositAccountFlag());
            Assertions.assertNull(actual.getDepositAccountList());
            assertObjectAreEqual(paymentInformation, actual.getPaymentInformation());
            assertObjectAreEqual(masterBiller, actual.getMasterBillerResponse());
            Assertions.assertNull(actual.getDefaultPaymentOverride());
        }
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionTypeIncorrect_ShouldThrowTMBCommonException() throws TMBCommonException {
        transactionType = "incorrect-transaction-type";
        paymentInformation.setTransactionType(transactionType);
        setUpAsyncHelper();
        mockGetCacheReturnSuccess(cache, paymentInformation);
        mockFetchCommonPaymentRuleByTypeAndCodeReturnBillPayRuleSuccess(commonPaymentRule);
        mockFetchBillerDetailByCompCodeReturnSuccess(masterBiller);
        mockFetchCustomerKYCReturnSuccess(customerDetailResponse);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers));

        Assertions.assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
    }

    @Test
    void testGetCommonPaymentMethod_WhenPrepareDataAsyncException_ShouldThrowTMBCommonException() throws TMBCommonException {
        transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
        paymentInformation.setTransactionType(transactionType);
        mockGetCacheReturnSuccess(cache, paymentInformation);
        mockAsyncHelperExecuteRequestAsyncThrowTMBCommonException();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers));

        Assertions.assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testGetCommonPaymentMethod_WhenInterruptedException_ShouldThrowTMBCommonException() throws TMBCommonException {
        transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
        paymentInformation.setTransactionType(transactionType);
        mockGetCacheReturnSuccess(cache, paymentInformation);
        mockAsyncHelperExecuteRequestAsyncThrowInterruptedException();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers));

        Assertions.assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());

    }

    @Test
    void testGetCommonPaymentMethod_WhenDataCacheNotFound_ShouldThrowTMBCommonException() throws TMBCommonException {
        mockGetCacheThrowNotFoundException();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers));

        Assertions.assertEquals(ResponseCode.TRANSACTION_NOT_FOUND_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
    }

    @Nested
    class WowPointTest {
        @Test
        void testAddWowPointToCache_WithValidData_ShouldSaveWowPointConfigurationToCache() throws JsonProcessingException {
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(wowPoint);
            prepareData.setCommonPaymentRule(commonPaymentRule);

            doNothing().when(cacheService).set(anyString(), anyString(), any(CommonPaymentDraftCache.class));

            TestUtils.invokeMethod(paymentMethodCommonPaymentService, "saveWowPointConfigurationToCache", cache, prepareData, transactionId);

            verify(cacheService, times(1)).set(
                    eq(COMMON_PAYMENT_CACHE_PREFIX + transactionId),
                    eq(COMMON_PAYMENT_HASH_KEY_CACHE),
                    any(CommonPaymentDraftCache.class)
            );
        }

        @Test
        void testAddWowPointToCache_WhenCacheIsNull_ShouldNotSaveWowPointConfigurationToCache() throws JsonProcessingException {
            cache = null;
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(wowPoint);
            prepareData.setCommonPaymentRule(commonPaymentRule);

            TestUtils.invokeMethod(paymentMethodCommonPaymentService, "saveWowPointConfigurationToCache", cache, prepareData, transactionId);

            verify(cacheService, never()).set(anyString(), anyString(), any());
        }

        @Test
        void testAddWowPointToCache_WhenPrepareDataIsNull_ShouldNotSaveWowPointConfigurationToCache() throws JsonProcessingException {
            prepareData = null;

            TestUtils.invokeMethod(paymentMethodCommonPaymentService, "saveWowPointConfigurationToCache", cache, prepareData, transactionId);

            verify(cacheService, never()).set(anyString(), anyString(), any());
        }

        @Test
        void testAddWowPointToCache_WhenTransactionIdIsNull_ShouldNotSaveWowPointConfigurationToCache() throws JsonProcessingException {
            transactionId = null;
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(wowPoint);
            prepareData.setCommonPaymentRule(commonPaymentRule);

            TestUtils.invokeMethod(paymentMethodCommonPaymentService, "saveWowPointConfigurationToCache", cache, prepareData, transactionId);

            verify(cacheService, never()).set(anyString(), anyString(), any());
        }

        @Test
        void testAddWowPointToCache_WhenCommonPaymentRuleIsNull_ShouldNotSaveWowPointConfigurationToCache() throws JsonProcessingException {
            prepareData.setCommonPaymentRule(null);

            TestUtils.invokeMethod(paymentMethodCommonPaymentService, "saveWowPointConfigurationToCache", cache, prepareData, transactionId);

            verify(cacheService, never()).set(anyString(), anyString(), any());
        }

        @Test
        void testAddWowPointToCache_WhenWowPointFlagIsFalse_ShouldNotSaveWowPointConfigurationToCache() throws JsonProcessingException {
            commonPaymentRule.setWowPointFlag(false);
            commonPaymentRule.setWowPoint(wowPoint);
            prepareData.setCommonPaymentRule(commonPaymentRule);

            TestUtils.invokeMethod(paymentMethodCommonPaymentService, "saveWowPointConfigurationToCache", cache, prepareData, transactionId);

            verify(cacheService, never()).set(anyString(), anyString(), any());
        }

        @Test
        void testSaveWowPointConfigurationToCache_WhenJsonProcessingExceptionOccurs_ShouldThrowTMBCommonException() throws Throwable {
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(wowPoint);
            prepareData.setCommonPaymentRule(commonPaymentRule);

            doThrow(JsonProcessingException.class).when(cacheService).set(anyString(), anyString(), any(CommonPaymentDraftCache.class));

            Assertions.assertThrows(TMBCommonException.class, () -> TestUtils.invokeMethodWithThrow(paymentMethodCommonPaymentService, "saveWowPointConfigurationToCache", cache, prepareData, transactionId));
        }
    }

    @Nested
    class ScheduleTest {
        @Test
        void testGetCommonPaymentMethod_WhenScheduleTransaction_ShouldReturnWowPointFlagFalse() throws TMBCommonException {
            Schedule scheduleRequest = new Schedule().setScheduleName("schedule-name");

            transactionType = COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
            defaultPayment = DEFAULT_PAYMENT_DEPOSIT;
            paymentInformation.setSchedule(scheduleRequest);
            paymentInformation.setTransactionType(transactionType);
            commonPaymentRule.setDefaultPayment(defaultPayment);
            setUpAsyncHelper();
            mockGetCacheReturnSuccess(cache, paymentInformation);
            mockFetchCommonPaymentRuleByTypeAndCodeReturnBillPayRuleSuccess(commonPaymentRule);
            mockFetchBillerDetailByCompCodeReturnSuccess(masterBiller);
            mockFetchCustomerKYCReturnSuccess(customerDetailResponse);
            mockProcessBillPayReturnDepositAccountListSuccess(depositAccountList);

            PaymentMethodCommonPaymentResponse actual = paymentMethodCommonPaymentService.getCommonPaymentMethod(transactionId, headers);

            Assertions.assertFalse(actual.isWowPointFlag());
            Assertions.assertNull(actual.getWowPoint());
        }
    }


    private void mockGetCacheThrowNotFoundException() throws TMBCommonException {
        when(cacheService.get(anyString(), anyString(), eq(CommonPaymentDraftCache.class))).thenThrow(CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_NOT_FOUND_ERROR));
    }

    private void mockGetCacheReturnPaymentDetailWithoutCodeSuccess(CommonPaymentDraftCache cache, PaymentInformation paymentInformation) throws TMBCommonException {
        String compCodeNull = null;
        paymentInformation.setRequireAddressFlag(true)
                .setCompCode(compCodeNull)
                .setFundCode("fund-code-bill-pay")
                .setProductDetail(new ProductDetail())
                .setAmountDetail(new AmountDetail())
                .setCompleteScreenDetail(new CompleteScreenDetail());

        cache.setPaymentInformation(paymentInformation);
        cache.setCrmId(crmId);
        when(cacheService.get(anyString(), anyString(), eq(CommonPaymentDraftCache.class))).thenReturn(cache);
    }

    private void setUpAsyncHelper() throws TMBCommonException {
        when(asyncHelper.executeRequestAsync(any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
            return CompletableFuture.completedFuture(Objects.requireNonNull(feignResponseEntity.getBody()).getData());
        });
    }

    private void mockGetCacheReturnSuccess(CommonPaymentDraftCache cache, PaymentInformation paymentInformation) throws TMBCommonException {
        PaymentMethodCommonPaymentServiceTest.this.paymentInformation.setRequireAddressFlag(true)
                .setCompCode("come-code-bill-pay")
                .setFundCode("fund-code-bill-pay")
                .setProductDetail(new ProductDetail())
                .setAmountDetail(new AmountDetail())
                .setCompleteScreenDetail(new CompleteScreenDetail())
                .setReviewScreenDetail(new ReviewScreenDetail()
                        .setRemarkIconFlag(true)
                        .setRemarkDescEn("remarkDescEn")
                        .setRemarkDescTh("remarkDescTh")
                        .setRemarkTitleEn("remarkTitleEn")
                        .setRemarkTitleTh("remarkTitleTh"));

        cache.setPaymentInformation(paymentInformation);
        cache.setCrmId(crmId);
        when(cacheService.get(anyString(), anyString(), eq(CommonPaymentDraftCache.class))).thenReturn(cache);
    }

    private void mockFetchCommonPaymentRuleByTypeAndCodeReturnBillPayRuleSuccess(CommonPaymentRule commonPaymentRule) {
        commonPaymentRule.setId("id")
                .setCode("code")
                .setType("type")
                .setCouponFlag(true)
                .setCreditCardInstallmentsFlag(true)
                .setCreditCardOtherFlag(true)
                .setCreditCardPointFlag(true)
                .setQrCodeFlag(true)
                .setWowPointFlag(true)
                .setWowPoint(new WowPoint()
                        .setMinPaymentAmount(new BigDecimal(100))
                        .setMin(new BigDecimal(10))
                        .setMax(new BigDecimal(10000))
                        .setConversionRate(new ConversionRateDetail(new BigDecimal(1), new BigDecimal(10)))
                );

        TmbServiceResponse<CommonPaymentRule> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(commonPaymentRule);
        when(paymentServiceClient.fetchCommonPaymentRuleByTypeAndCode(correlationId, cache.getPaymentInformation().getTransactionType(), cache.getPaymentInformation().getCompCode())).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockFetchCommonPaymentRuleByTypeReturnBillPayRuleSuccess(CommonPaymentRule commonPaymentRule) {
        commonPaymentRule.setId("id")
                .setCode("code")
                .setType("type")
                .setCouponFlag(true)
                .setCreditCardInstallmentsFlag(true)
                .setCreditCardOtherFlag(true)
                .setCreditCardPointFlag(true)
                .setQrCodeFlag(true)
                .setWowPointFlag(true)
                .setWowPoint(new WowPoint()
                        .setMinPaymentAmount(new BigDecimal(100))
                        .setMin(new BigDecimal(10))
                        .setMax(new BigDecimal(10000))
                        .setConversionRate(new ConversionRateDetail(new BigDecimal(1), new BigDecimal(10)))
                );

        TmbServiceResponse<CommonPaymentRule> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(commonPaymentRule);
        when(paymentServiceClient.fetchCommonPaymentRuleByType(correlationId, cache.getPaymentInformation().getTransactionType())).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockFetchCustomerKYCReturnSuccess(CustomerDetailResponse customerDetailResponse) {
        customerDetailResponse.setCustomerNameEn("customer-name-en-value")
                .setCustomerNameTh("customer-name-th-value")
                .setContactAddress("contact-address-value")
                .setRegisterAddress("register-address-value");

        TmbServiceResponse<List<CustomerDetailResponse>> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(List.of(customerDetailResponse));
        when(customerServiceClient.fetchCustomerSearchRealtime(eq(correlationId), any(CustomerSearchRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockFetchCustomerKYCReturnOnlyRegisterAddressSuccess(CustomerDetailResponse customerDetailResponse) {
        String contactAddressNull = null;
        customerDetailResponse.setCustomerNameEn("customer-name-en-value")
                .setCustomerNameTh("customer-name-th-value")
                .setContactAddress(contactAddressNull)
                .setRegisterAddress("register-address-value");
        TmbServiceResponse<List<CustomerDetailResponse>> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(List.of(customerDetailResponse));
        when(customerServiceClient.fetchCustomerSearchRealtime(eq(correlationId), any(CustomerSearchRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockFetchBillerDetailByCompCodeReturnSuccess(MasterBillerResponse masterBiller) {
        masterBiller.setBillerInfo(new BillerInfoResponse());
        TmbServiceResponse<MasterBillerResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(masterBiller);
        when(paymentServiceClient.fetchMasterBillerByCompCode(correlationId, cache.getPaymentInformation().getCompCode())).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockFetchBillerDetailByCompCodeReturnCreditCardBillerSuccess(MasterBillerResponse masterBiller) {
        masterBiller.setBillerInfo(new BillerInfoResponse());
        masterBiller.getBillerInfo().setCreditCardFlag(true);
        TmbServiceResponse<MasterBillerResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(masterBiller);
        when(paymentServiceClient.fetchMasterBillerByCompCode(correlationId, cache.getPaymentInformation().getCompCode())).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockProcessBillPayReturnDepositAccountListSuccess(List<DepositAccount> depositAccountList) throws TMBCommonException {
        ProcessBillPayDataTemp t = new ProcessBillPayDataTemp();
        t.setDepositAccountList(depositAccountList);
        when(billPayPaymentMethodCommonPaymentHelper.processBillPay(any(CommonPaymentRule.class), any(MasterBillerResponse.class), any(HttpHeaders.class)))
                .thenReturn(t);
    }

    private void mockProcessBillPayReturnCreditCardAccountListSuccess(List<CreditCardSupplementary> creditCardList) throws TMBCommonException {
        CreditCardSupplementary c = new CreditCardSupplementary();
        c.setCardNo("card-no");
        c.setAccountId("account-id");
        c.setProductNameEn("product-name-en");
        c.setProductNameTh("product-name-th");
        c.setCardPoints(new CreditCardPoint());
        c.getCardPoints().setPointEarned("point-earned");
        c.getCardPoints().setPointUsed("point-used");
        c.getCardPoints().setPointAvailable("point-available");
        c.getCardPoints().setPointRemain("point-remain");
        c.getCardPoints().setExpiryDate("expiry-date");
        c.getCardPoints().setExpiryPoints("expiry-points");
        creditCardList.add(c);
        ProcessBillPayDataTemp t = new ProcessBillPayDataTemp();
        t.setCreditCardList(creditCardList);
        when(billPayPaymentMethodCommonPaymentHelper.processBillPay(any(CommonPaymentRule.class), any(MasterBillerResponse.class), any(HttpHeaders.class)))
                .thenReturn(t);
    }

    private void mockProcessBillPayReturnIsCallCreditCardErrorAndDepositAccountListSuccess(List<CreditCardSupplementary> creditCardList) throws TMBCommonException {
        ProcessBillPayDataTemp t = new ProcessBillPayDataTemp();
        t.setCreditCardList(null);
        t.setDepositAccountList(depositAccountList);
        when(billPayPaymentMethodCommonPaymentHelper.processBillPay(any(CommonPaymentRule.class), any(MasterBillerResponse.class), any(HttpHeaders.class)))
                .thenReturn(t);
    }

    private void mockProcessBillPayThrowMissingConfigurationError() throws TMBCommonException {
        when(billPayPaymentMethodCommonPaymentHelper.processBillPay(any(CommonPaymentRule.class), any(MasterBillerResponse.class), any(HttpHeaders.class)))
                .thenThrow(TMBCommonException.class);
    }

    private void mockProcessBillPayReturnNoEligibleAccount(List<DepositAccount> depositAccountList, List<CreditCardSupplementary> creditCardAccountList) throws TMBCommonException {
        depositAccountList = null;
        creditCardAccountList = null;

        ProcessBillPayDataTemp noEligibleAccountResponse = new ProcessBillPayDataTemp();
        noEligibleAccountResponse.setDepositAccountList(depositAccountList);
        noEligibleAccountResponse.setCreditCardList(creditCardAccountList);
        when(billPayPaymentMethodCommonPaymentHelper.processBillPay(any(), any(), any())).thenReturn(noEligibleAccountResponse);
    }

    private void mockGetAccountIssueDebitCardReturnIssueDebitCardAccountListSuccess(List<DepositAccount> depositAccountList) throws TMBCommonException {
        DepositAccount dStatementAccount = new DepositAccount();
        dStatementAccount.setAccountName("issue-debit-card-account");
        depositAccountList.add(dStatementAccount);
        when(issueDebitCardAccountCommonPaymentService.getAccountList(eq(correlationId), eq(crmId)))
                .thenReturn(depositAccountList);
    }

    private void mockGetAccountIssueDebitCardThrowNoEligibleAccountError() throws TMBCommonException {
        when(issueDebitCardAccountCommonPaymentService.getAccountList(eq(correlationId), eq(crmId)))
                .thenThrow(CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR));

    }

    private void mockGetAccountDStatementReturnDStatementAccountListSuccess(List<DepositAccount> depositAccountList) throws TMBCommonException {
        DepositAccount dStatementAccount = new DepositAccount();
        dStatementAccount.setAccountName("d-statement-account");
        depositAccountList.add(dStatementAccount);
        when(dStatementAccountCommonPaymentService.getAccountList(eq(correlationId), eq(crmId)))
                .thenReturn(depositAccountList);
    }

    private void mockGetAccountDStatementThrowNoEligibleAccountError() throws TMBCommonException {
        when(dStatementAccountCommonPaymentService.getAccountList(eq(correlationId), eq(crmId)))
                .thenThrow(CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR));

    }

    private void mockAsyncHelperExecuteRequestAsyncThrowTMBCommonException() throws TMBCommonException {
        TMBCommonException cause = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), "message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);
        CompletableFuture<Object> failedFuture = CompletableFuture.failedFuture(cause);
        when(asyncHelper.executeRequestAsync(any())).thenReturn(failedFuture);
    }

    @SuppressWarnings("unchecked")
    private void mockAsyncHelperExecuteRequestAsyncThrowInterruptedException() throws TMBCommonException {
        CompletableFuture<AccountSaving> accountSavingFuture = new CompletableFuture<>();
        accountSavingFuture.completeExceptionally(new InterruptedException("Simulated InterruptedException"));
        when(asyncHelper.executeRequestAsync(any(Supplier.class))).thenReturn(accountSavingFuture);
    }

    private static <T> void assertObjectAreEqual(T expect, T actual) {
        org.assertj.core.api.Assertions.assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expect);
    }

    private void assertMappingCreditCardPointCorrectly(List<CreditCardSupplementary> creditCardAccountList, PaymentMethodCommonPaymentResponse actual) {
        Assertions.assertEquals(creditCardAccountList.get(0).getCardNo(), actual.getCreditCardPoint().get(0).getCardNo());
        Assertions.assertEquals(creditCardAccountList.get(0).getAccountId(), actual.getCreditCardPoint().get(0).getAccountId());
        Assertions.assertEquals(creditCardAccountList.get(0).getProductNameEn(), actual.getCreditCardPoint().get(0).getProductNameEn());
        Assertions.assertEquals(creditCardAccountList.get(0).getProductNameTh(), actual.getCreditCardPoint().get(0).getProductNameTh());
        Assertions.assertEquals(creditCardAccountList.get(0).getCardPoints().getPointEarned(), actual.getCreditCardPoint().get(0).getPointEarned());
        Assertions.assertEquals(creditCardAccountList.get(0).getCardPoints().getPointUsed(), actual.getCreditCardPoint().get(0).getPointUsed());
        Assertions.assertEquals(creditCardAccountList.get(0).getCardPoints().getPointAvailable(), actual.getCreditCardPoint().get(0).getPointAvailable());
        Assertions.assertEquals(creditCardAccountList.get(0).getCardPoints().getPointRemain(), actual.getCreditCardPoint().get(0).getPointRemain());
        Assertions.assertEquals(creditCardAccountList.get(0).getCardPoints().getExpiryDate(), actual.getCreditCardPoint().get(0).getExpiryDate());
        Assertions.assertEquals(creditCardAccountList.get(0).getCardPoints().getExpiryPoints(), actual.getCreditCardPoint().get(0).getExpiryPoints());
    }

}
