package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CommonAuthenticationClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthForceFR;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRefResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRule;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import feign.FeignException;
import feign.Request;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.nio.charset.Charset;
import java.util.Collections;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonAuthenticationServiceTest {

    @Mock
    private FeignClientHelper feignClientHelper;

    @Mock
    private CommonAuthenticationClient commonAuthenticationClient;

    @InjectMocks
    private CommonAuthenticationService commonAuthenticationService;

    private CommonAuthenVerifyRefRequest request;
    private String correlationId;
    private String crmId;
    private String ipAddress;
    CommonAuthenVerifyRule rule;

    @BeforeEach
    void setUp() {
        correlationId = "correlationId";
        crmId = "crmId";
        ipAddress = "0.0.0.0";

        request = new CommonAuthenVerifyRefRequest();
        request.setRefId("refId123");
        request.setFlowName("PAYMENT_CONFIRMATION");

        rule = new CommonAuthenVerifyRule().setDailyAmount("1000.50").setFeatureId(COMMON_PAYMENT_BILL_PAY_FEATURE_ID).setAmount("500.50").setCompCode("2704");
    }

    @Test
    void testVerifyReferenceIdWhenSuccessShouldNotThrowException() throws Exception {
        CommonAuthenVerifyRefResponse authResponse = initCommonAuthenResponseReturnDataSameAsRule();

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(authResponse);

        assertDoesNotThrow(() -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));
    }

    private CommonAuthenVerifyRefResponse initCommonAuthenResponseReturnDataSameAsRule() {
        CommonAuthenVerifyRefResponse authResponse = new CommonAuthenVerifyRefResponse();
        authResponse.setResult(true);
        authResponse.setAmount(rule.getAmount());
        authResponse.setFeatureId(rule.getFeatureId());
        authResponse.setDailyAmount(rule.getDailyAmount());
        authResponse.setBillerCode(rule.getCompCode());
        return authResponse;
    }

    @Test
    void testVerifyReferenceIdWhenAuthenticationFailedShouldThrowException() throws TMBCommonException {
        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenThrow(new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.BAD_REQUEST, null));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));

        assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void testVerifyReferenceIdWhenGotFeignExceptionShouldThrowException() throws TMBCommonException {

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenThrow(new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));

        assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
        verify(feignClientHelper).executeRequestOrElseThrow(any(), any());
    }

    @Test
    void testVerifyReferenceIdWhenFailedToValidateRuleCauseAmountNotMatchingShouldThrowTMBCommonException() throws TMBCommonException {
        String notMatchingValue = "987654.50";
        CommonAuthenVerifyRefResponse authResponse = initCommonAuthenResponseReturnDataSameAsRule();
        authResponse.setAmount(notMatchingValue);

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(authResponse);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));

        assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testVerifyReferenceIdWhenFailedToValidateRuleCauseFeatureIdNotMatchingShouldThrowTMBCommonException() throws TMBCommonException {
        String notMatchingValue = "incorrect-value";
        CommonAuthenVerifyRefResponse authResponse = initCommonAuthenResponseReturnDataSameAsRule();
        authResponse.setFeatureId(notMatchingValue);

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(authResponse);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));

        assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testVerifyReferenceIdWhenFailedToValidateRuleCauseBankCodeNotMatchingShouldThrowTMBCommonException() throws TMBCommonException {
        String notMatchingValue = "incorrect-value";
        CommonAuthenVerifyRefResponse authResponse = initCommonAuthenResponseReturnDataSameAsRule();
        authResponse.setBillerCode(notMatchingValue);

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(authResponse);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));

        assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testVerifyReferenceIdWhenFailedToValidateRuleCauseDailyAmountNotMatchingShouldThrowTMBCommonException() throws TMBCommonException {
        String notMatchingValue = "785.54";
        CommonAuthenVerifyRefResponse authResponse = initCommonAuthenResponseReturnDataSameAsRule();
        authResponse.setDailyAmount(notMatchingValue);

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(authResponse);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));

        assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testVerifyReferenceIdWhenBillerCodeAndCompCodeAreBlankThenShouldNotThrowException() throws TMBCommonException {
        CommonAuthenVerifyRefResponse authResponse = initCommonAuthenResponseReturnDataSameAsRule();
        authResponse.setBillerCode("");
        rule.setCompCode("");

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(authResponse);

        assertDoesNotThrow(() -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));
    }

    @Test
    void testVerifyReferenceIdWhenAmountHasMoreThanTwoDecimalPlacesThenShouldCompareWithTwoDecimalPlaces() throws TMBCommonException {
        CommonAuthenVerifyRefResponse authResponse = initCommonAuthenResponseReturnDataSameAsRule();
        authResponse.setAmount("500.505");
        rule.setAmount("500.50");

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(authResponse);

        assertDoesNotThrow(() -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));
    }

    @Test
    void testVerifyReferenceIdWhenDailyAmountHasMoreThanTwoDecimalPlacesThenShouldCompareWithTwoDecimalPlaces() throws TMBCommonException {
        CommonAuthenVerifyRefResponse authResponse = initCommonAuthenResponseReturnDataSameAsRule();
        authResponse.setDailyAmount("1000.509");
        rule.setDailyAmount("1000.50");

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(authResponse);

        assertDoesNotThrow(() -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));
    }

    @Test
    void testVerifyReferenceIdWhenCallCommonAuthenticationClientThenShouldReturnSuccess() throws TMBCommonException {
        CommonAuthenVerifyRefResponse expectedResponse = initCommonAuthenResponseReturnDataSameAsRule();
        TmbServiceResponse<CommonAuthenVerifyRefResponse> tmbResponse = new TmbServiceResponse<>();
        tmbResponse.setData(expectedResponse);
        ResponseEntity<TmbServiceResponse<CommonAuthenVerifyRefResponse>> responseEntity = ResponseEntity.ok(tmbResponse);

        when(commonAuthenticationClient.verifyCommonAuthentication(correlationId, crmId, ipAddress, request)).thenReturn(responseEntity);

        setUpFeignClientHelperExecuteRequest();

        CommonAuthenVerifyRefResponse actualResponse = commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule);

        assertEquals(expectedResponse, actualResponse);
        verify(commonAuthenticationClient).verifyCommonAuthentication(correlationId, crmId, ipAddress, request);
    }

    @Test
    void testVerifyReferenceIdWhenCommonAuthenticationClientThrowsExceptionThenShouldThrowException() throws TMBCommonException {
        Request feignRequest = Request.create(Request.HttpMethod.POST, "/authentication", Collections.emptyMap(), request.toString().getBytes(), Charset.defaultCharset(), null);

        when(commonAuthenticationClient.verifyCommonAuthentication(correlationId, crmId, ipAddress, request)).thenThrow(new FeignException.InternalServerError("", feignRequest, request.toString().getBytes(), Collections.emptyMap()));

        setUpFeignClientHelperExecuteRequest();

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, request, rule));

        assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCacheDataSuccessShouldNotThrowException() {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        response.setData("true");
        Mockito.when(commonAuthenticationClient.getCacheData(anyString(), anyString())).thenReturn(ResponseEntity.ok(response));

        assertDoesNotThrow(() -> commonAuthenticationService.getCacheData(correlationId, "key"));
    }

    @Test
    void testGetCacheDataFailShouldThrowException() {
        Mockito.when(commonAuthenticationClient.getCacheData(anyString(), anyString())).thenThrow(FeignException.class);

        assertThrows(FeignException.class, () -> commonAuthenticationService.getCacheData(correlationId, "key"));
    }

    @Nested
    class GetCommonAuthForceFRTest {
        @Test
        void testGetCommonAuthForceFR_WhenSuccess_ShouldReturnDataNull() {
            TmbServiceResponse<CommonAuthForceFR> tmbServiceResponse = new TmbServiceResponse<>();
            tmbServiceResponse.setData(null);
            tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
            when(commonAuthenticationClient.getCommonAuthForceFR(correlationId, crmId)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

            assertDoesNotThrow(() -> commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId));
        }

        @Test
        void testGetCommonAuthForceFR_WhenFailedFeignException_ShouldDoesNotThrow() {
            when(commonAuthenticationClient.getCommonAuthForceFR(correlationId, crmId)).thenThrow(FeignException.class);

            assertDoesNotThrow(() -> commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId));
        }
    }

    @Nested
    class ExecuteRequestDataCanBeNullTest {
        @Test
        void testExecuteRequestDataCanBeNull_WhenSuccessDataNull_ShouldDoesNotThrow() {
            TmbServiceResponse<Object> tmbServiceResponse = new TmbServiceResponse<>();
            tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
            tmbServiceResponse.setData(null);
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = () -> ResponseEntity.ok(tmbServiceResponse);

            ResponseEntity<TmbServiceResponse<?>> actual = TestUtils.invokeMethod(commonAuthenticationService, "executeRequestDataCanBeNull", supplier);

            assertNull(actual);
        }

        @Test
        void testExecuteRequestDataCanBeNull_WhenFailedFeignException_ShouldDoesNotThrow() {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplierException = () -> {
                throw new FeignException.InternalServerError("", null, null, null);
            };

            assertThrows(TMBCommonException.class, () -> TestUtils.invokeMethodWithThrow(commonAuthenticationService, "executeRequestDataCanBeNull", supplierException));
        }

        @Test
        void testExecuteRequestDataCanBeNull_WhenFailedNullPointerException_ShouldDoesNotThrow() {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = () -> ResponseEntity.ok().body(null);

            assertThrows(TMBCommonException.class, () -> TestUtils.invokeMethodWithThrow(commonAuthenticationService, "executeRequestDataCanBeNull", supplier));
        }

        @Test
        void testExecuteRequestDataCanBeNull_WhenFailedStatusCodeNotSuccess_ShouldDoesNotThrow() {
            Status status = new Status();
            status.setCode(ResponseCode.FAILED_V2.getCode());
            TmbServiceResponse<Object> tmbServiceResponse = new TmbServiceResponse<>();
            tmbServiceResponse.setStatus(status);
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = () -> ResponseEntity.ok().body(tmbServiceResponse);

            assertThrows(TMBCommonException.class, () -> TestUtils.invokeMethodWithThrow(commonAuthenticationService, "executeRequestDataCanBeNull", supplier));
        }

        @Test
        void testExecuteRequestDataCanBeNull_WhenFailedHttpStatusInternalServerError_ShouldDoesNotThrow() {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = () -> ResponseEntity.internalServerError().body(null);

            assertThrows(TMBCommonException.class, () -> TestUtils.invokeMethodWithThrow(commonAuthenticationService, "executeRequestDataCanBeNull", supplier));
        }
    }

    private void setUpFeignClientHelperExecuteRequest() throws TMBCommonException {
        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            try {
                ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
                return feignResponseEntity.getBody().getData();
            } catch (Exception e) {
                Supplier<TMBCommonException> errorSupplier = invocation.getArgument(1);
                throw errorSupplier.get();
            }
        });
    }
}