package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PaymentStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PaymentStatusPublisherServiceTest {

    @Mock
    private KafkaProducerService kafkaProducerService;

    private PaymentStatusPublisherService paymentStatusPublisherService;

    private final String paymentStatusTopic = "payment_status_topic";

    @BeforeEach
    void setUp() {
        paymentStatusPublisherService = new PaymentStatusPublisherService(kafkaProducerService, paymentStatusTopic);
    }

    @Test
    void testPublish_ShouldPublishToCorrectTopic() {
        PaymentStatus paymentStatus = new PaymentStatus();
        paymentStatus.setTransactionId("test-tx-123");

        paymentStatusPublisherService.publish(paymentStatus, "identifier-123");

        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> keyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass(Map.class);
        verify(kafkaProducerService).sendMessageAsync(topicCaptor.capture(), keyCaptor.capture(), payloadCaptor.capture(), headersCaptor.capture());

        assertEquals(paymentStatusTopic, topicCaptor.getValue());
        assertEquals("identifier-123", headersCaptor.getValue().get("entry_id"));
    }


    @Test
    void testPublish_WhenKafkaThrowsException_ShouldHandleGracefully() {
        PaymentStatus paymentStatus = new PaymentStatus();
        paymentStatus.setTransactionId("test-tx-error-123");
        String identifier = "identifier-error-123";
        doThrow(new RuntimeException("Kafka is down")).when(kafkaProducerService).sendMessageAsync(anyString(), anyString(), anyString(), anyMap());

        assertDoesNotThrow(() -> paymentStatusPublisherService.publish(paymentStatus, identifier));
    }

}