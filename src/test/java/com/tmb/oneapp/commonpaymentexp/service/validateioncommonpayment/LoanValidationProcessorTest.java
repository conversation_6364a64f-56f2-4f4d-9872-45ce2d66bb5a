package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.Transaction;
import com.tmb.common.cache.service.TransactionServices;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.model.BillerCreditcardMerchant;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ConversionRateDetail;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.model.WowPoint;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccountPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.WaiveOCP;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.Balances;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.HomeLoanFullInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.LoanAccountHomeLoan;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.AccountService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerAccountBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_METHOD_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper.LOAN_ACCOUNT_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LoanValidationProcessorTest {
    @InjectMocks
    LoanValidationProcessor loanValidationProcessor;

    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private PaymentService paymentService;
    @Mock
    private CustomerService customerService;
    @Mock
    private BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CacheService cacheService;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    @Mock
    private CustomerAccountBizService customerAccountBizService;
    @Mock
    private BaseBillPayValidator baseBillPayValidator;
    @Mock
    private AccountService accountService;
    @Mock
    private TransactionServices transactionServices;


    String crmId;
    String correlationId;
    HttpHeaders headers;
    ValidationCommonPaymentRequest request;
    CommonPaymentDraftCache cacheData;
    CommonPaymentConfig commonPaymentConfig;
    String compCode;
    String depositAccountNumber;
    MasterBillerResponse masterBillerResponse;

    @BeforeEach
    void setUp() throws TMBCommonException {

        Transaction.setTransactionServices(transactionServices);

        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        cacheData = new CommonPaymentDraftCache();
        compCode = "comp-code";
        depositAccountNumber = "**********";
        request = new ValidationCommonPaymentRequest();

        commonPaymentConfig = new CommonPaymentConfig();

        masterBillerResponse = new MasterBillerResponse().setBillerInfo(new BillerInfoResponse());
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);

        TestUtils.setUpAsyncHelperExecuteMethodAsync(asyncHelper);
    }

    @Nested
    class ExecuteLoanValidation {
        @Test
        void testExecuteLoan_WhenMakeLoanTransaction_ShouldSuccess() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
            cacheData = initialCacheDataForLoanTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchLoanAccountByLoanAccountID();
            mockFetchLoanAccountList();
            mockFetchCustomerCrmProfile();
            mockGetAccountByAccountNumber();
            mockValidateDailyLimitExceededDoNothing();
            mockStaticTransactionGenerateId();
            mockValidateLoanPayment();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();


            ValidationCommonPaymentResponse actual = loanValidationProcessor.executeValidate(request, headers, cacheData);

            Assertions.assertNotNull(actual.getFee());
            Assertions.assertNotNull(actual.getTransactionId());
            Assertions.assertNotNull(actual.getTotalAmount());
            Assertions.assertTrue(actual.getIsRequireCommonAuthen());
            assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
            assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
            Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());

            assertValidateCommonValidation();
            assertWriteActivityLog();
            assertSaveCache();
        }

        @Test
        void testExecuteLoan_WhenMakeLoanWithPayByOwnerTransaction_ShouldSkipCheckDailyLimitSuccess() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
            String ownAccount = LOAN_ACCOUNT_PREFIX + "reference1" + "reference2";
            cacheData = initialCacheDataForLoanTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchLoanAccountByLoanAccountID();
            mockFetchLoanAccountListReturnOwnAccount(ownAccount);
            mockFetchCustomerCrmProfile();
            mockGetAccountByAccountNumber();
            mockStaticTransactionGenerateId();
            mockValidateLoanPayment();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();

            assertDoesNotThrow(() -> loanValidationProcessor.executeValidate(request, headers, cacheData));

            verify(dailyLimitService, never()).validateDailyLimitExceeded(any(), any(), any());
            assertValidateCommonValidation();
            assertWriteActivityLog();
            assertSaveCache();
        }

        @Test
        void testExecuteLoan_WhenFailedTMBCommonExceptionAtPrePareDateExecuteMethodAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
            cacheData = initialCacheDataForLoanTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();
            TMBCommonException expectedException = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), "message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);
            mockFetchCustomerCrmProfileThrowException(expectedException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> loanValidationProcessor.executeValidate(request, headers, cacheData));

            assertEquals(expectedException, exception);
            assertWriteActivityLog();
        }

        @Test
        void testExecuteLoan__WhenFailedNullPointerExceptionAtPrePareDateExecuteMethodAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
            cacheData = initialCacheDataForLoanTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();
            mockFetchCustomerCrmProfileThrowException(new NullPointerException(""));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> loanValidationProcessor.executeValidate(request, headers, cacheData));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        }
    }

    @Nested
    class ExecuteLoanValidationWithWowTransaction {
        @Test
        void testExecuteLoan_WhenMakeLoanTransaction_ShouldSuccess() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
            prepareWowPointUsageData();

            compCode = "AL02";
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchLoanAccountByLoanAccountID();
            mockFetchLoanAccountList();
            mockFetchCustomerCrmProfile();
            mockGetAccountByAccountNumber();
            mockValidateDailyLimitExceededDoNothing();
            mockStaticTransactionGenerateId();
            mockValidateLoanPayment();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();


            ValidationCommonPaymentResponse actual = loanValidationProcessor.executeValidate(request, headers, cacheData);

            Assertions.assertNotNull(actual.getFee());
            Assertions.assertNotNull(actual.getTransactionId());
            Assertions.assertNotNull(actual.getTotalAmount());
            Assertions.assertTrue(actual.getIsRequireCommonAuthen());
            assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
            assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
            Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());

            assertValidateCommonValidation();
            assertWriteActivityLog();
            assertSaveCache();
        }

        @Test
        void testExecuteLoan_WhenMakeLoanTransactionWithNullAdditionalParams_ShouldSuccess() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
            prepareWowPointUsageData();

            compCode = "AL02";
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchLoanAccountByLoanAccountID();
            mockFetchLoanAccountList();
            mockFetchCustomerCrmProfile();
            mockGetAccountByAccountNumber();
            mockValidateDailyLimitExceededDoNothing();
            mockStaticTransactionGenerateId();

            OCPBillPaymentResponse ocpBillPaymentResponse = new OCPBillPaymentResponse();
            OCPBillPayment ocpResponse = new OCPBillPayment();
            ocpResponse.setToAccount(new OCPAccountPayment()
                    .setAccountId("**********")
                    .setAccountType("CDA")
                    .setFiId("")
                    .setTitle(null)
            );
            ocpResponse.setFromAccount(new OCPAccountPayment()
                    .setAccountId("**********")
                    .setAccountType("SDA")
                    .setFiId("")
                    .setTitle(null)
            );
            ocpResponse.setAdditionalParams(null);
            ocpResponse.setAmount("9500.50");
            ocpResponse.setRef1("Ref1");
            ocpResponse.setRef2("Ref2");
            ocpResponse.setRef3("Ref3");
            ocpResponse.setRef4("Ref4");
            ocpResponse.setBankRefId("BankRefId");
            ocpResponse.setBankRefId("BankRefId");
            ocpResponse.setFee(new OCPFee().setPaymentFee("10.50"));

            ocpBillPaymentResponse.setData(ocpResponse);
            when(paymentService.validateOCPBillPayment(anyString(), anyString(), any(OCPBillRequest.class))).thenReturn(ocpBillPaymentResponse);

            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();


            ValidationCommonPaymentResponse actual = loanValidationProcessor.executeValidate(request, headers, cacheData);

            Assertions.assertNotNull(actual.getFee());
            Assertions.assertNotNull(actual.getTransactionId());
            Assertions.assertNotNull(actual.getTotalAmount());
            Assertions.assertTrue(actual.getIsRequireCommonAuthen());
            assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
            assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
            Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());

            assertValidateCommonValidation();
            assertWriteActivityLog();
            assertSaveCache();
        }

        @Test
        void testExecuteLoan_WhenMakeLoanWithPayByOwnerTransaction_ShouldSkipCheckDailyLimitSuccess() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
            prepareWowPointUsageData();
            String ownAccount = LOAN_ACCOUNT_PREFIX + "reference1" + "reference2";

            compCode = "AL02";
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchLoanAccountByLoanAccountID();
            mockFetchLoanAccountListReturnOwnAccount(ownAccount);
            mockFetchCustomerCrmProfile();
            mockGetAccountByAccountNumber();
            mockStaticTransactionGenerateId();
            mockValidateLoanPayment();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();

            assertDoesNotThrow(() -> loanValidationProcessor.executeValidate(request, headers, cacheData));

            verify(dailyLimitService, never()).validateDailyLimitExceeded(any(), any(), any());
            assertValidateCommonValidation();
            assertWriteActivityLog();
            assertSaveCache();
        }

        @Test
        void testExecuteLoan_WhenFailedTMBCommonExceptionAtPrePareDateExecuteMethodAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
            prepareWowPointUsageData();

            compCode = "AL02";
            TMBCommonException expectedException = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), "message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);
            mockFetchCustomerCrmProfileThrowException(expectedException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> loanValidationProcessor.executeValidate(request, headers, cacheData));

            assertEquals(expectedException, exception);
            assertWriteActivityLog();
        }

        @Test
        void testExecuteLoan__WhenFailedNullPointerExceptionAtPrePareDateExecuteMethodAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
            prepareWowPointUsageData();
            mockFetchCustomerCrmProfileThrowException(new NullPointerException(""));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> loanValidationProcessor.executeValidate(request, headers, cacheData));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        }

        @Test
        void testExecuteLoan_WhenMakeHomeLoanTransactionAndPayWithCreditCard_ShouldNotCallValidateWowPoint() throws TMBCommonException, JsonProcessingException, TMBCommonExceptionWithResponse {
            prepareWowPointUsageData();
            request = initialDefaultRequest();
            request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setPayWithCreditCardFlag(true));
            compCode = "AL02";
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchLoanAccountByLoanAccountID();
            mockFetchLoanAccountList();
            mockFetchCustomerCrmProfile();
            mockGetAccountByAccountNumber();
            mockValidateDailyLimitExceededDoNothing();
            mockStaticTransactionGenerateId();
            mockValidateLoanPayment();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();


            ValidationCommonPaymentResponse actual = loanValidationProcessor.executeValidate(request, headers, cacheData);

            Assertions.assertNotNull(actual.getFee());
            Assertions.assertNotNull(actual.getTransactionId());
            Assertions.assertNotNull(actual.getTotalAmount());
            Assertions.assertTrue(actual.getIsRequireCommonAuthen());
            assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
            assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
            Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
            verify(baseBillPayValidator, never()).validateWowPoint(any(), any());

            assertValidateCommonValidation();
            assertWriteActivityLog();
            assertSaveCache();
        }

        private void prepareWowPointUsageData() {
            cacheData = initialCacheDataForLoanTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(new WowPoint()
                    .setMinPaymentAmount(new BigDecimal(100))
                    .setMin(new BigDecimal(10))
                    .setMax(new BigDecimal(10000))
                    .setConversionRate(new ConversionRateDetail(new BigDecimal(2), new BigDecimal(15))));
            cacheData.setCommonPaymentRule(commonPaymentRule);
            cacheData.getPaymentInformation().setCompCode("AL02");
            request = initialDefaultRequest();
            request.setWowPoint(new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(1500))
                    .setDiscountAmount(new BigDecimal(200)));
        }
    }

    @Nested
    class ValidateDataInLoanProcessor {
        @Test
        void testValidateAccruedDebt_WhenDeptLessThanAmount_ShouldThrowTMBCommonException() {
            String deptPayoffLessThanAmount = "10.00";
            BigDecimal amount = new BigDecimal("99999.00");

            HomeLoanFullInfoResponse homeLoanFullInfoResponse = new HomeLoanFullInfoResponse()
                    .setAccount(new LoanAccountHomeLoan()
                            .setBalances(
                                    new Balances().setPayoff(deptPayoffLessThanAmount)
                            )
                    );

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> TestUtils.invokeMethodWithThrow(loanValidationProcessor, "validateAccruedDebt", homeLoanFullInfoResponse, amount));

            assertEquals(ResponseCode.ACCRUED_DEBT_ERROR.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }

        @Test
        void testTransformRequest_ShouldTransformRequest() {
            ProductDetail productDetail = new ProductDetail()
                    .setProductRef1("lowercase1")
                    .setProductRef2("lowercase2");

            assertDoesNotThrow(() -> TestUtils.invokeMethod(loanValidationProcessor, "transformRequestBody", productDetail));

            assertEquals("LOWERCASE1", productDetail.getProductRef1());
            assertEquals("LOWERCASE2", productDetail.getProductRef2());
        }
    }

    @Nested
    class ValidateCalculateFee {
        @Test
        void testCalculateFeeWhenWaiveFlagIsYThenReturnZeroFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee("25.00");
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("Y");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment, new DepositAccount());

            assertEquals(new BigDecimal("0.00"), result);
        }

        @Test
        void testCalculateFeeWhenWaiveFlagFromDepositAccountTrueThenReturnZeroFee() {
            String waiveFromDepositAccount = "1";

            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee("25.00");
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("N");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            DepositAccount fromDepositAccount = new DepositAccount();
            fromDepositAccount.setWaiveFeeForBillpay(waiveFromDepositAccount);
            BigDecimal result = invokeCalculateFee(ocpBillPayment, fromDepositAccount);

            assertEquals(new BigDecimal("0.00"), result);
        }

        @Test
        void testCalculateFeeWhenHasBillPmtFeeThenReturnBillPmtFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee("25.00");
            fee.setPaymentFee("15.00");
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("N");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment, new DepositAccount());

            assertEquals(new BigDecimal("25.00"), result);
        }

        @Test
        void testCalculateFeeWhenHasPaymentFeeButNoBillPmtFeeThenReturnPaymentFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee(null);
            fee.setPaymentFee("15.00");
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("N");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment, new DepositAccount());

            assertEquals(new BigDecimal("15.00"), result);
        }

        @Test
        void testCalculateFeeWhenNoFeesAvailableThenReturnZeroFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee(null);
            fee.setPaymentFee(null);
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("N");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment, new DepositAccount());

            assertEquals(new BigDecimal("0.00"), result);
        }

        @Test
        void testCalculateFeeWhenWaiveIsNullThenShouldNotThrowException() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee("25.00");
            ocpBillPayment.setFee(fee);

            ocpBillPayment.setWaive(null);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment, new DepositAccount());

            assertEquals(new BigDecimal("25.00"), result);
        }

        @ParameterizedTest
        @CsvSource({
                "25.00, 15.00, N, SDA, 25.00",
                "null, 15.00, N, SDA, 15.00",
                "null, null, N, SDA, 0.00",
                "25.00, 15.00, Y, SDA, 0.00"
        })
        void testCalculateFeeWithVariousParameters(String billPmtFee, String paymentFee, String waiveFlag, String accountType, String expectedFee) {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();

            if (!"null".equals(billPmtFee)) {
                fee.setBillPmtFee(billPmtFee);
            }

            if (!"null".equals(paymentFee)) {
                fee.setPaymentFee(paymentFee);
            }

            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag(waiveFlag);
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType(accountType);
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment, new DepositAccount());

            assertEquals(new BigDecimal(expectedFee), result);
        }

    }

    @Test
    void testHandleException_WhenNotTMBCommonException_ShouldThrowTMBCommonException() {
        RuntimeException unHandleExceptionClass = new RuntimeException();

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> loanValidationProcessor.handleException(null, null, null, null, null, null, unHandleExceptionClass));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
    }

    @Test
    void testGetProcessorType_ShouldReturnLoan() {
        String actual = loanValidationProcessor.getProcessorType();

        Assertions.assertEquals(BILLER_PAYMENT_TMB_LOAN, actual);
    }

    @Nested
    class HandleExceptionTest {
        @Test
        void testHandleException_WhenFailedTMBCommonException_ShouldThrowTMBCommonException() {
            assertThrows(TMBCommonException.class, () -> loanValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonException("ignore message")));
        }

        @Test
        void testHandleException_WhenFailedTMBCommonExceptionWithResponse_ShouldThrowTMBCommonExceptionWithResponse() {
            assertThrows(TMBCommonExceptionWithResponse.class, () -> loanValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonExceptionWithResponse("", "", "", HttpStatus.OK, null, null)));
        }
    }

    private BigDecimal invokeCalculateFee(OCPBillPayment ocpBillPayment, DepositAccount fromDepositAccount) {
        return TestUtils.invokeMethod(loanValidationProcessor, "calculateFee", ocpBillPayment, fromDepositAccount);
    }

    private ValidationCommonPaymentRequest initialDefaultRequest() {
        request = new ValidationCommonPaymentRequest();
        request.setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(true).setAccountNumber(depositAccountNumber).setAmount(BigDecimal.valueOf(9500.50)));
        request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setPayWithCreditCardFlag(false));
        request.setFlow("flow");
        request.setTransactionId("transaction_id");
        return request;
    }

    private CommonPaymentDraftCache initialCacheDataForLoanTransaction() {
        PaymentInformation p = new PaymentInformation()
                .setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY)
                .setTransactionType("bill_pay")
                .setCompCode(compCode)
                .setFundCode("ignore")
                .setRequireAddressFlag(true)
                .setProductDetail(new ProductDetail()
                        .setProductNameEn("Product-name-en-value")
                        .setProductNameTh("Product-name-th-value")
                        .setProductAttributeList(List.of(
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("1000000006")
                                        .setValueTh("1000000006"),
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("0987834783")
                                        .setValueTh("0987834783")
                        ))
                        .setProductRef1("reference1")
                        .setProductRef2("reference2")
                )
                .setAmountDetail(new AmountDetail()
                        .setAmountLabelEn("Amount-lable-en-value")
                        .setAmountLabelTh("Amount-lable-th-value")
                        .setAmountUnitEn("Amount-unit-en-value")
                        .setAmountUnitTh("Amount-unit-th-value")
                        .setAmountValue(BigDecimal.valueOf(9855.00))
                )
                .setCompleteScreenDetail(new CompleteScreenDetail()
                        .setRemarkEn("Remark-en-value")
                        .setRemarkTh("Remark-th-value")
                        .setFooterEn("Footer-en-value")
                        .setFooterTh("Footer-th-value")
                        .setBackBtnKeyEn("Back-btn-key-en-value")
                        .setBackBtnKeyTh("Back-btn-key-th-value")
                        .setBackBtnUrl("www.google.co.th")
                );

        return new CommonPaymentDraftCache()
                .setPaymentInformation(p)
                .setCrmId(crmId)
                .setCommonPaymentConfig(commonPaymentConfig);
    }

    private void mockFetchBillPayConfig() throws TMBCommonException {
        BillerCreditcardMerchant e1 = new BillerCreditcardMerchant();
        e1.setMerchantId("merchantId");
        List<BillerCreditcardMerchant> billerCreditCardMerchant = List.of(e1);
        BillPayConfiguration billPayConfig = new BillPayConfiguration().setBillerCreditcardMerchant(billerCreditCardMerchant);

        when(paymentService.getBillPayConfig(correlationId)).thenReturn(billPayConfig);
    }

    private void mockFetchMasterBillerByCompCode(MasterBillerResponse masterBiller) throws TMBCommonException {
        BillerInfoResponse billerInfoResponse = masterBiller.getBillerInfo();
        billerInfoResponse.setStartTime(null);
        billerInfoResponse.setEndTime(null);
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        billerInfoResponse.setPaymentMethod("5");
        billerInfoResponse.setBillerMethod(BILLER_METHOD_TMB_LOAN);
        billerInfoResponse.setNameEn("PB_PRUDENTIAL LIFE ASSURANCE (THAILAND) PCL");
        billerInfoResponse.setBillerCategoryCode("03");

        masterBiller.setRef1(new ReferenceResponse().setIsMobile(false));

        when(paymentService.getMasterBiller(correlationId, compCode)).thenReturn(masterBiller);
    }

    private void mockFetchCustomerCrmProfile() throws TMBCommonException {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile()
                .setPaymentAccuUsgAmt(BigDecimal.valueOf(100.50))
                .setBillpayAccuUsgAmt(BigDecimal.valueOf(2000.50))
                .setEbAccuUsgAmtDaily(2000.50);
        when(customerService.getCustomerCrmProfile(correlationId, crmId)).thenReturn(customerCrmProfile);
    }

    private void mockGetAccountByAccountNumber() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount()
                .setAccountNumber(depositAccountNumber)
                .setAccountType("SDA")
                .setAvailableBalance(BigDecimal.valueOf(999999.99));
        when(billPayAccountCommonPaymentService.getAccountByAccountNumber(depositAccountNumber, headers)).thenReturn(depositAccount);
    }

    private void mockFetchLoanAccountByLoanAccountID() throws TMBCommonException {
        String ref1 = cacheData.getPaymentInformation().getProductDetail().getProductRef1();
        String ref2 = cacheData.getPaymentInformation().getProductDetail().getProductRef2();
        String loanAccountId = LOAN_ACCOUNT_PREFIX + ref1 + ref2;
        HomeLoanFullInfoResponse homeLoanFullInfoResponse = new HomeLoanFullInfoResponse();
        homeLoanFullInfoResponse.setAccount(new LoanAccountHomeLoan());
        homeLoanFullInfoResponse.getAccount().setBalances(new Balances());
        homeLoanFullInfoResponse.getAccount().getBalances().setPayoff("9999999");
        when(accountService.fetchLoanAccountByLoanAccountID(correlationId, loanAccountId)).thenReturn(homeLoanFullInfoResponse);
    }

    private void mockFetchLoanAccountList() throws TMBCommonException {
        when(customerAccountBizService.fetchLoanAccount(correlationId, crmId)).thenReturn(List.of(new LoanAccount().setAccountNumber("account-number")));
    }

    private void mockFetchLoanAccountListReturnOwnAccount(String ownAccount) throws TMBCommonException {
        when(customerAccountBizService.fetchLoanAccount(correlationId, crmId)).thenReturn(List.of(new LoanAccount().setAccountNumber(ownAccount)));
    }

    private void mockValidateDailyLimitExceededDoNothing() throws TMBCommonException {
        doNothing().when(dailyLimitService).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
    }

    private void mockStaticTransactionGenerateId() {
        when(Transaction.getTransactionId(anyString(), anyInt())).thenReturn("transaction-after-generate");
    }

    private void mockValidateLoanPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        OCPBillPaymentResponse ocpBillPaymentResponse = new OCPBillPaymentResponse();
        OCPBillPayment ocpResponse = new OCPBillPayment();
        ocpResponse.setToAccount(new OCPAccountPayment()
                .setAccountId("**********")
                .setAccountType("CDA")
                .setFiId("")
                .setTitle(null)
        );
        ocpResponse.setFromAccount(new OCPAccountPayment()
                .setAccountId("**********")
                .setAccountType("SDA")
                .setFiId("")
                .setTitle(null)
        );
        ocpResponse.setAdditionalParams(List.of(new AdditionalParam().setName("Msg").setValue("|AdditionalParamValue")));
        ocpResponse.setAmount("9500.50");
        ocpResponse.setRef1("Ref1");
        ocpResponse.setRef2("Ref2");
        ocpResponse.setRef3("Ref3");
        ocpResponse.setRef4("Ref4");
        ocpResponse.setBankRefId("BankRefId");
        ocpResponse.setBankRefId("BankRefId");
        ocpResponse.setFee(new OCPFee().setPaymentFee("10.50"));

        ocpBillPaymentResponse.setData(ocpResponse);
        when(paymentService.validateOCPBillPayment(anyString(), anyString(), any(OCPBillRequest.class))).thenReturn(ocpBillPaymentResponse);
    }

    private void mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth() throws TMBCommonException {
        when(dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new CommonAuthenResult().setRequireCommonAuthen(true));
    }

    private void mockFetchCustomerCrmProfileThrowException(Exception expectedException) throws TMBCommonException {
        when(customerService.getCustomerCrmProfile(correlationId, crmId)).thenThrow(expectedException);
    }

    private void assertValidateCommonValidation() throws TMBCommonException {
        verify(baseBillPayValidator, times(1)).validateCompCodeExclusion(any(BillPayConfiguration.class), eq(compCode));
        verify(baseBillPayValidator, times(1)).validateServiceHours(any(MasterBillerResponse.class));
        verify(baseBillPayValidator, times(1)).validateBillerExpiration(any(MasterBillerResponse.class));
    }

    private void assertSaveCache() throws JsonProcessingException {
        verify(cacheService, times(1)).set(anyString(), eq(COMMON_PAYMENT_HASH_KEY_CACHE), any());
    }

    private void assertWriteActivityLog() {
        verify(logEventPublisherService, times(1)).saveActivityLog(any());
    }

}