package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableRunnable;
import com.tmb.oneapp.commonpaymentexp.model.WowPoint;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PaymentStatus;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.CallbackInitialRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.Collections;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_IS_USED;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BaseConfirmServiceHelperImplTest {
    @InjectMocks
    private BaseConfirmServiceHelperImpl baseConfirmServiceHelper;

    @Mock
    private CacheService cacheService;
    @Mock
    private CustomerService customerService;
    @Mock
    private CallbackConfirmService callbackConfirmService;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private CustomersTransactionService customersTransactionService;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private NotificationCommonPaymentService notificationCommonPaymentService;

    private HttpHeaders headers;
    private String crmId;
    private String correlationId;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);

        TestUtils.setUpAsyncExecuteMethodAsyncSafelyVoid(asyncHelper);
    }

    @Test
    void testBaseClearDraftDataCache_ShouldSuccess() {
        baseConfirmServiceHelper.baseClearDraftDataCache("transactionId");

        String expectFullTransaction = COMMON_PAYMENT_CACHE_PREFIX + "transactionId";
        verify(cacheService, times(1)).delete(expectFullTransaction);
    }

    @Nested
    class UpdatePinFreeCountWithConditionTest {

        @Test
        void testBaseUpdatePinFreeCountWithCondition_WhenNotRequireCommonAuth_ShouldCallUpdatePinFreeCount() throws TMBCommonException {
            CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
            customerCrmProfile.setPinFreeTxnCount(1);
            customerCrmProfile.setPinFreeSeetingFlag("Y");

            baseConfirmServiceHelper.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, false);

            int expectCount = customerCrmProfile.getPinFreeTxnCount() + 1;
            verify(customerService, times(1)).updatePinFreeCount(crmId, expectCount, correlationId);
        }

        @Test
        void testBaseUpdatePinFreeCountWithCondition_WhenNotRequireCommonAuthButPinFreeSettingFlagIsN_ShouldNotCallUpdatePinFreeCount() throws TMBCommonException {
            CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
            customerCrmProfile.setPinFreeTxnCount(1);
            customerCrmProfile.setPinFreeSeetingFlag("N");

            baseConfirmServiceHelper.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, false);

            verify(customerService, never()).updatePinFreeCount(anyString(), anyInt(), anyString());
        }

        @Test
        void testBaseUpdatePinFreeCountWithCondition_WhenRequireCommonAuth_ShouldNotCallUpdatePinFreeCount() throws TMBCommonException {
            boolean isRequireCommonAuth = true;
            CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
            customerCrmProfile.setPinFreeTxnCount(1);
            customerCrmProfile.setPinFreeSeetingFlag("Y");

            baseConfirmServiceHelper.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, isRequireCommonAuth);

            verify(customerService, never()).updatePinFreeCount(anyString(), anyInt(), anyString());
        }
    }

    @Nested
    class BaseExecuteCallbackIfConfiguredAsyncTest {

        @Test
        void testBaseExecuteCallbackIfConfiguredAsync_ShouldCallBack() {
            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setPaymentInformation(new PaymentInformation()
                            .setCompCode("compCode")
                            .setProductDetail(new ProductDetail()
                                    .setProductRef1("productRef1")
                            )
                    )
                    .setCommonPaymentConfig(new CommonPaymentConfig()
                            .setCallbackUrls(Collections.singletonList("intra.ttb.com/callback"))
                    );

            baseConfirmServiceHelper.baseExecuteCallbackIfConfiguredAsync(draftCache, "transactionTime", "10.00");

            verify(callbackConfirmService, times(1)).callback(eq(draftCache), any(PaymentStatus.class));
        }

        @Test
        void testBaseExecuteCallbackIfConfiguredAsync_WhenMissingConfigCallBackUrl_ShouldCallBack() {
            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setPaymentInformation(new PaymentInformation()
                            .setCompCode("compCode")
                            .setProductDetail(new ProductDetail()
                                    .setProductRef1("productRef1")
                            )
                    )
                    .setCommonPaymentConfig(new CommonPaymentConfig()
                            .setCallbackUrls(Collections.emptyList())
                    );

            baseConfirmServiceHelper.baseExecuteCallbackIfConfiguredAsync(draftCache, "transactionTime", "10.00");

            verify(callbackConfirmService, times(1)).callback(any(CommonPaymentDraftCache.class), any(PaymentStatus.class));
        }

        @Test
        void testBaseExecuteCallbackIfConfiguredAsync_WhenCallbackEnabled_ShouldBuildPaymentStatusWithCallback() {
            String callbackUrl = "http://test-callback.com";
            CallbackInitialRequest callbackInitialRequest = new CallbackInitialRequest();
            callbackInitialRequest.setEnableCallbackFlag(true);
            callbackInitialRequest.setCallbackUrl(callbackUrl);

            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setPaymentInformation(new PaymentInformation()
                            .setCallback(callbackInitialRequest)
                    );

            baseConfirmServiceHelper.baseExecuteCallbackIfConfiguredAsync(draftCache, "transactionTime", "transactionId");

            ArgumentCaptor<PaymentStatus> paymentStatusCaptor = ArgumentCaptor.forClass(PaymentStatus.class);
            verify(callbackConfirmService, times(1)).callback(eq(draftCache), paymentStatusCaptor.capture());

            PaymentStatus capturedStatus = paymentStatusCaptor.getValue();
            assertNotNull(capturedStatus.getCallback());
            assertEquals(callbackUrl, capturedStatus.getCallback().getUrl());
            assertEquals("", capturedStatus.getCallback().getPayload());
        }
    }

    @Nested
    class BaseHandleExceptionTest {

        @Test
        void testBaseHandleException_WhenTMBCommonException_ShouldThrowCorrectTMBCommonException() {
            TMBCommonException throwException = CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> baseConfirmServiceHelper.baseHandleException(new ConfirmationCommonPaymentRequest(), throwException));

            assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
            verify(cacheService, times(1)).delete(anyString(), eq(COMMON_PAYMENT_HASH_KEY_IS_USED));
        }

        @Test
        void testBaseHandleException_WhenTMBCommonExceptionWithResponse_ShouldThrowCorrectTMBCommonExceptionWithResponse() {
            TMBCommonExceptionWithResponse throwException = new TMBCommonExceptionWithResponse(ResponseCode.ETE_ERROR.getCode(), "test", ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null, "title");

            TMBCommonExceptionWithResponse exception = assertThrows(TMBCommonExceptionWithResponse.class,
                    () -> baseConfirmServiceHelper.baseHandleException(new ConfirmationCommonPaymentRequest(), throwException));

            assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
            verify(cacheService, times(1)).delete(anyString(), eq(COMMON_PAYMENT_HASH_KEY_IS_USED));
        }

        @Test
        void testBaseHandleException_WhenUnhandledException_ShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            NullPointerException throwException = new NullPointerException("Unhandled exception");

            TMBCommonException exception = baseConfirmServiceHelper.baseHandleException(new ConfirmationCommonPaymentRequest(), throwException);

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
            verify(cacheService, times(1)).delete(anyString(), eq(COMMON_PAYMENT_HASH_KEY_IS_USED));
        }
    }

    @Nested
    class GetBasePrepareDataConfirmTest {
        @Test
        void testGetBasePrepareDataConfirm_WhenSuccess_ShouldReturnData() throws TMBCommonException {
            CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
            when(customerService.getCustomerCrmProfile(correlationId, crmId)).thenReturn(customerCrmProfile);

            BasePrepareDataConfirm actual = baseConfirmServiceHelper.getBasePrepareDataConfirm(headers, "transactionTime");

            assertEquals(customerCrmProfile, actual.getCustomerCrmProfile());
            assertEquals("transactionTime", actual.getTransactionTime());
        }

        @Test
        void testGetBasePrepareDataConfirm_WhenFailedTmbCommonException_ShouldThrowTMBCommonException() throws TMBCommonException {
            when(customerService.getCustomerCrmProfile(correlationId, crmId)).thenThrow(TMBCommonException.class);

            assertThrows(TMBCommonException.class, () -> baseConfirmServiceHelper.getBasePrepareDataConfirm(headers, "transactionTime"));
        }
    }

    @Nested
    class BaseConfirmDataAfterConfirmExternalTest {

        @Test
        void testBaseConfirmDataAfterConfirmExternal_WhenSuccess_ShouldReturnNull() throws TMBCommonException {
            ConfirmationCommonPaymentRequest confirmRequest = new ConfirmationCommonPaymentRequest().setTransactionId("transactionId");
            boolean isRequireCommonAuthen = false;
            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setValidateDraftCache(new ValidationCommonPaymentDraftCache()
                            .setRequireCommonAuthen(isRequireCommonAuthen)
                            .setExternalConfirmRequest(new ExternalConfirmRequest()
                                    .setOcpBillPaymentConfirmRequest(new OCPBillRequest().setEpayCode("e_pay_code"))))
                    .setCommonPaymentConfig(new CommonPaymentConfig()
                            .setCallbackUrls(Collections.singletonList("callbackUrl")))
                    .setValidateRequest(new ValidationCommonPaymentRequest()
                            .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("1000.00"))))
                    .setPaymentInformation(new PaymentInformation()
                            .setCompCode("comp_code")
                            .setProductDetail(new ProductDetail()
                                    .setProductRef1("productRef1")));

            BasePrepareDataConfirm prepareData = new BasePrepareDataConfirm()
                    .setTransactionTime(String.valueOf(System.currentTimeMillis()))
                    .setCustomerCrmProfile(new CustomerCrmProfile()
                            .setPinFreeSeetingFlag("Y")
                            .setPinFreeTxnCount(0));

            BaseConfirmDataAfterConfirmExternal actual = baseConfirmServiceHelper.baseConfirmDataAfterConfirmExternal(confirmRequest, headers, draftCache, prepareData);

            assertNull(actual);

            verify(cacheService, times(1)).delete(COMMON_PAYMENT_CACHE_PREFIX + confirmRequest.getTransactionId());
            verify(customersTransactionService, times(1)).clearDepositCache(correlationId, crmId);

            // test when not require common auth
            verify(customerService, times(1)).updatePinFreeCount(crmId, 1, correlationId);

            verify(dailyLimitService, times(1)).updateAccumulateUsage(draftCache, prepareData.getCustomerCrmProfile(), crmId, correlationId);

            // test when not has CommonPaymentConfig
            verify(callbackConfirmService, times(1)).callback(eq(draftCache), any(PaymentStatus.class));
            verify(notificationCommonPaymentService, times(1)).sendENotification(any(NotificationCommonPayment.class));
        }

        @Test
        void testBaseConfirmDataAfterConfirmExternal_WhenPayWithWowTransactionSuccess_ShouldReturnNull() throws TMBCommonException {
            ConfirmationCommonPaymentRequest confirmRequest = new ConfirmationCommonPaymentRequest().setTransactionId("transactionId");
            boolean isRequireCommonAuthen = false;
            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setValidateDraftCache(new ValidationCommonPaymentDraftCache()
                            .setRequireCommonAuthen(isRequireCommonAuthen)
                            .setExternalConfirmRequest(new ExternalConfirmRequest()
                                    .setOcpBillPaymentConfirmRequest(new OCPBillRequest().setEpayCode("e_pay_code"))))
                    .setCommonPaymentConfig(new CommonPaymentConfig()
                            .setCallbackUrls(Collections.singletonList("callbackUrl")))
                    .setValidateRequest(new ValidationCommonPaymentRequest()
                            .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("1000.00")))
                            .setCreditCard(new CreditCardValidationCommonPaymentRequest().setPayWithCreditCardFlag(false))
                            .setWowPoint(new WowPointValidationCommonPaymentRequest().setWowPointAmount(new BigDecimal(1500)).setDiscountAmount(new BigDecimal(100))))
                    .setPaymentInformation(new PaymentInformation()
                            .setCompCode("comp_code")
                            .setProductDetail(new ProductDetail()
                                    .setProductRef1("productRef1")))
                    .setCommonPaymentRule(new CommonPaymentRuleInCache(true, new WowPoint()));

            BasePrepareDataConfirm prepareData = new BasePrepareDataConfirm()
                    .setTransactionTime(String.valueOf(System.currentTimeMillis()))
                    .setCustomerCrmProfile(new CustomerCrmProfile()
                            .setPinFreeSeetingFlag("Y")
                            .setPinFreeTxnCount(0));

            BaseConfirmDataAfterConfirmExternal actual = baseConfirmServiceHelper.baseConfirmDataAfterConfirmExternal(confirmRequest, headers, draftCache, prepareData);

            assertNull(actual);

            verify(cacheService, times(1)).delete(COMMON_PAYMENT_CACHE_PREFIX + confirmRequest.getTransactionId());
            verify(customersTransactionService, times(1)).clearDepositCache(correlationId, crmId);

            // test when not require common auth
            verify(customerService, times(1)).updatePinFreeCount(crmId, 1, correlationId);

            verify(dailyLimitService, times(1)).updateAccumulateUsage(draftCache, prepareData.getCustomerCrmProfile(), crmId, correlationId);

            // test when not has CommonPaymentConfig
            verify(callbackConfirmService, times(1)).callback(eq(draftCache), any(PaymentStatus.class));
            verify(notificationCommonPaymentService, times(1)).sendENotification(any(NotificationCommonPayment.class));
        }

        @Test
        void testBaseConfirmDataAfterConfirmExternal_WhenIsRequireCommonAuth_ShouldDoesNotCallUpdatePinFreeCount() throws TMBCommonException {
            ConfirmationCommonPaymentRequest confirmRequest = new ConfirmationCommonPaymentRequest().setTransactionId("transactionId");
            boolean isRequireCommonAuthen = true;
            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setValidateDraftCache(new ValidationCommonPaymentDraftCache()
                            .setRequireCommonAuthen(isRequireCommonAuthen)
                            .setExternalConfirmRequest(new ExternalConfirmRequest()
                                    .setOcpBillPaymentConfirmRequest(new OCPBillRequest().setEpayCode("e_pay_code"))))
                    .setCommonPaymentConfig(new CommonPaymentConfig()
                            .setCallbackUrls(Collections.singletonList("callbackUrl")))
                    .setValidateRequest(new ValidationCommonPaymentRequest()
                            .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("1000.00"))))
                    .setPaymentInformation(new PaymentInformation()
                            .setCompCode("comp_code")
                            .setProductDetail(new ProductDetail()
                                    .setProductRef1("productRef1")));

            BasePrepareDataConfirm prepareData = new BasePrepareDataConfirm()
                    .setTransactionTime(String.valueOf(System.currentTimeMillis()))
                    .setCustomerCrmProfile(new CustomerCrmProfile()
                            .setPinFreeSeetingFlag("Y")
                            .setPinFreeTxnCount(0));

            BaseConfirmDataAfterConfirmExternal actual = baseConfirmServiceHelper.baseConfirmDataAfterConfirmExternal(confirmRequest, headers, draftCache, prepareData);

            assertNull(actual);

            verify(customerService, never()).updatePinFreeCount(anyString(), anyInt(), anyString());

            verify(cacheService, times(1)).delete(COMMON_PAYMENT_CACHE_PREFIX + confirmRequest.getTransactionId());
            verify(customersTransactionService, times(1)).clearDepositCache(correlationId, crmId);
            verify(dailyLimitService, times(1)).updateAccumulateUsage(draftCache, prepareData.getCustomerCrmProfile(), crmId, correlationId);
            verify(callbackConfirmService, times(1)).callback(eq(draftCache), any(PaymentStatus.class));
            verify(notificationCommonPaymentService, times(1)).sendENotification(any(NotificationCommonPayment.class));
        }

        @Test
        void testBaseConfirmDataAfterConfirmExternal_WhenNotHasCommonPaymentConfigApiKey_ShouldReturnNull() throws TMBCommonException {
            CommonPaymentConfig commonPaymentConfigNullApiKey = new CommonPaymentConfig();
            boolean isRequireCommonAuthen = false;
            ConfirmationCommonPaymentRequest confirmRequest = new ConfirmationCommonPaymentRequest().setTransactionId("transactionId");
            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setValidateDraftCache(new ValidationCommonPaymentDraftCache()
                            .setRequireCommonAuthen(isRequireCommonAuthen)
                            .setExternalConfirmRequest(new ExternalConfirmRequest()
                                    .setOcpBillPaymentConfirmRequest(new OCPBillRequest().setEpayCode("e_pay_code"))))
                    .setCommonPaymentConfig(commonPaymentConfigNullApiKey.setCallbackUrls(Collections.emptyList()))
                    .setValidateRequest(new ValidationCommonPaymentRequest()
                            .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("1000.00"))))
                    .setPaymentInformation(new PaymentInformation()
                            .setCompCode("comp_code")
                            .setProductDetail(new ProductDetail()
                                    .setProductRef1("productRef1")));

            BasePrepareDataConfirm prepareData = new BasePrepareDataConfirm()
                    .setTransactionTime(String.valueOf(System.currentTimeMillis()))
                    .setCustomerCrmProfile(new CustomerCrmProfile()
                            .setPinFreeSeetingFlag("Y")
                            .setPinFreeTxnCount(0));

            BaseConfirmDataAfterConfirmExternal actual = baseConfirmServiceHelper.baseConfirmDataAfterConfirmExternal(confirmRequest, headers, draftCache, prepareData);

            assertNull(actual);

            verify(callbackConfirmService, times(1)).callback(any(CommonPaymentDraftCache.class), any(PaymentStatus.class));

            verify(cacheService, times(1)).delete(COMMON_PAYMENT_CACHE_PREFIX + confirmRequest.getTransactionId());
            verify(customersTransactionService, times(1)).clearDepositCache(correlationId, crmId);
            verify(customerService, times(1)).updatePinFreeCount(crmId, 1, correlationId);
            verify(dailyLimitService, times(1)).updateAccumulateUsage(draftCache, prepareData.getCustomerCrmProfile(), crmId, correlationId);
            verify(notificationCommonPaymentService, times(1)).sendENotification(any(NotificationCommonPayment.class));
        }
    }

    @Test
    void testExecuteMethodSafelyVoid_WhenException_ShouldDoesNotThrow() {
        ThrowableRunnable runnableThrowException = () -> {
            throw new TMBCommonException("");
        };

        assertDoesNotThrow(() -> TestUtils.invokeMethod(baseConfirmServiceHelper, "executeMethodSafelyVoid", runnableThrowException));
    }
}