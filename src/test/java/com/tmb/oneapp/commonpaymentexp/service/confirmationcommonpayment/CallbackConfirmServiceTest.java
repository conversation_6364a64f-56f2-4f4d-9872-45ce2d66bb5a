package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PaymentStatus;
import com.tmb.oneapp.commonpaymentexp.service.PaymentStatusPublisherService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class CallbackConfirmServiceTest {

    @Mock
    private PaymentStatusPublisherService paymentStatusPublisherService;

    @InjectMocks
    private CallbackConfirmService callbackConfirmService;

    @Test
    void testCallback_GivenPaymentStatusAndIsPartner_ShouldPublishNormally() {
        PaymentStatus status = PaymentStatus.builder().transactionId("tx-partner").build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("entry-partner");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName("shopee")
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, status);

        verify(paymentStatusPublisherService).publish(eq(status), eq("shopee"));
    }

    @Test
    void testCallback_GivenPaymentStatusAndIsNotPartner_ShouldPublishNormally() {
        PaymentStatus status = PaymentStatus.builder().transactionId("tx-normal").build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("entry-normal");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName(null)
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, status);

        verify(paymentStatusPublisherService).publish(eq(status), eq("entry-normal"));
    }


    @Test
    void testCallback_GivenNullPaymentStatus_ShouldNotPublish() {
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache();

        callbackConfirmService.callback(draftCache, null);

        verify(paymentStatusPublisherService, never()).publish(any(PaymentStatus.class), any(String.class));
    }

    @Test
    void testCallback_GivenEntryIdIsPb_ShouldBypassAndNotPublish() {
        PaymentStatus status = PaymentStatus.builder().transactionId("tx-pb").build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("pb");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName(null)
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, status);

        verify(paymentStatusPublisherService, never()).publish(any(PaymentStatus.class), any(String.class));
    }

    @Test
    void testCallback_GivenEntryIdIsPbIgnoreCase_ShouldBypassAndNotPublish() {
        PaymentStatus status = PaymentStatus.builder().transactionId("tx-PB").build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("PB");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName(null)
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, status);

        verify(paymentStatusPublisherService, never()).publish(any(PaymentStatus.class), any(String.class));
    }
}
