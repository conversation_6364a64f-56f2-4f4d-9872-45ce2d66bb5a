package com.tmb.oneapp.commonpaymentexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.Schedule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.InitializationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.InitializationValidator;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CALL_FROM_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InitializationCommonPaymentServiceTest {

    @Mock
    CacheService cacheService;

    @Mock
    PaymentService paymentService;

    @Mock
    InitializationValidator initializationValidator;

    InitializationCommonPaymentService initializationCommonPaymentService;

    private static final String TEST_DEEPLINK_PREFIX = "oneappvit://linkaccess/commonpayment";
    private static final int TEST_CACHE_EXPIRE_SECOND = 600;

    private HttpHeaders headers;
    private String correlationId;
    private String osVersion;
    private MasterBillerResponse masterBillerResponse;
    private InitializationCommonPaymentRequest request;

    @BeforeEach
    void setUp() throws TMBCommonException {
        initializationCommonPaymentService = new InitializationCommonPaymentService(
                cacheService, paymentService, initializationValidator, TEST_DEEPLINK_PREFIX, TEST_CACHE_EXPIRE_SECOND);

        correlationId = "test-correlation-id";
        osVersion = "iOS123";

        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.add(CommonPaymentExpConstant.HEADER_OS_VERSION, osVersion);
        headers.add(CommonPaymentExpConstant.HEADER_CLIENT_VERSION, "5.14.0");
        request = initialCommonPaymentRequest();

        masterBillerResponse = initialMasterBiller();
    }

    @Test
    void testInitialCommonPaymentWhenSuccessShouldNotThrowsException() throws TMBCommonException, JsonProcessingException {
        mockCommonPaymentConfigSuccess();
        mockGetMasterBiller(masterBillerResponse);

        InitializationCommonPaymentResponse response = initializationCommonPaymentService.initialCommonPayment(request, headers);

        assertNotNull(response);
        assertNotNull(response.getDeeplinkUrl());
        verify(cacheService).set(anyString(), anyString(), any(CommonPaymentDraftCache.class), anyInt());
        verify(initializationValidator).validateData(any(InitializationCommonPaymentRequest.class), any(HttpHeaders.class), any(InitialPrepareData.class));
    }

    @Test
    void testInitialCommonPaymentWhenCacheSetFailShouldThrowsException() throws JsonProcessingException, TMBCommonException {
        mockCommonPaymentConfigSuccess();
        doThrow(new JsonProcessingException("Simulated JSON processing error") {
        }).when(cacheService).set(anyString(), anyString(), any(CommonPaymentDraftCache.class), anyInt());
        mockGetMasterBiller(masterBillerResponse);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> initializationCommonPaymentService.initialCommonPayment(request, headers));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals("Can not parse request model to object", exception.getErrorMessage());
    }

    @Test
    void testInitialCommonPaymentRefIdShouldUnique() throws TMBCommonException, JsonProcessingException {
        mockCommonPaymentConfigSuccess();
        doNothing().when(cacheService).set(anyString(), anyString(), any(), anyInt());
        mockGetMasterBiller(masterBillerResponse);
        InitializationCommonPaymentResponse response1 = initializationCommonPaymentService.initialCommonPayment(request, headers);
        InitializationCommonPaymentResponse response2 = initializationCommonPaymentService.initialCommonPayment(request, headers);

        assertNotEquals(response1.getDeeplinkUrl(), response2.getDeeplinkUrl());
        verify(initializationValidator, times(2)).validateData(any(InitializationCommonPaymentRequest.class), any(HttpHeaders.class), any(InitialPrepareData.class));
    }

    @Test
    void testInitialCommonPaymentWhenTransactionTypeIsDStatementShouldReturnDStatement() throws TMBCommonException, JsonProcessingException {
        PaymentInformation paymentInformation = request.getPaymentInformation();
        paymentInformation.setTransactionType(COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT);
        request.setPaymentInformation(paymentInformation);
        mockCommonPaymentConfigSuccess();
        ArgumentCaptor<CommonPaymentDraftCache> commonPaymentDraftCacheArgumentCaptor = ArgumentCaptor.forClass(CommonPaymentDraftCache.class);
        doNothing().when(cacheService).set(anyString(), anyString(), commonPaymentDraftCacheArgumentCaptor.capture(), anyInt());
        InitializationCommonPaymentResponse actual = initializationCommonPaymentService.initialCommonPayment(request, headers);

        assertNotNull(actual);
        assertEquals(COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT, commonPaymentDraftCacheArgumentCaptor.getValue().getProcessorType());
        verify(paymentService, never()).getMasterBiller(anyString(), anyString());
    }

    @Nested
    class PrepareDataTest {
        @Captor
        private ArgumentCaptor<Supplier<TMBCommonException>> supplierCaptor;

        @Test
        void testInitialCommonPayment_WhenFailedGetMasterBillerException_ShouldThrowMasterBillerNotFoundException() throws TMBCommonException {
            TMBCommonException masterBillerNotFoundException = CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.MASTER_BILLER_NOT_FOUND_ERROR);
            when(paymentService.getMasterBillerOrElseThrow(any(), any(), any(), any(), supplierCaptor.capture())).thenThrow(masterBillerNotFoundException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> initializationCommonPaymentService.initialCommonPayment(request, headers));

            assertEquals(supplierCaptor.getValue().get().getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testInitialCommonPayment_WhenFailedGetCommonPaymentConfig_ShouldThrowTMBCommonException() throws TMBCommonException {
            when(paymentService.getCommonPaymentConfig(anyString(), anyString())).thenThrow(TMBCommonException.class);

            assertThrows(TMBCommonException.class, () -> initializationCommonPaymentService.initialCommonPayment(request, headers));
        }
    }

    @Nested
    class ScheduleTransactionTest {
        @Test
        void testInitialCommonPaymentWhenScheduleTransactionShouldSaveScheduleRequestIntoCache() throws TMBCommonException, JsonProcessingException {
            Schedule scheduleRequest = new Schedule().setScheduleName("scheduleName");
            request.getPaymentInformation().setSchedule(scheduleRequest);
            mockCommonPaymentConfigSuccess();

            assertDoesNotThrow(() -> initializationCommonPaymentService.initialCommonPayment(request, headers));

            ArgumentCaptor<CommonPaymentDraftCache> captor = ArgumentCaptor.forClass(CommonPaymentDraftCache.class);
            verify(cacheService, times(1)).set(anyString(), anyString(), captor.capture(), anyInt());
            CommonPaymentDraftCache actualCommonPaymentDraftCache = captor.getValue();
            assertObjectAreEquals(scheduleRequest, actualCommonPaymentDraftCache.getPaymentInformation().getSchedule());
            assertEquals(ProcessorConstant.PROCESSOR_SCHEDULE_UNIMPLEMENTED, actualCommonPaymentDraftCache.getProcessorType());
        }
    }

    @Nested
    class TransactionFromDeepLinkTest {
        @Test
        void testInitialCommonPaymentWhenTransactionFromDeeplinkShouldSaveDeeplinkIntoCache() throws TMBCommonException, JsonProcessingException {
            String entryIdFromCallFromDeepLink = "entry-id-from-call-from-deeplink";
            String entryId = request.getPaymentInformation().getEntryId();
            request.getPaymentInformation().setDeepLinkTransactionId("deep_link_transaction_id");
            DeepLinkRequest deepLinkRequest = new DeepLinkRequest()
                    .setAmount("123.45")
                    .setRef1(request.getPaymentInformation().getProductDetail().getProductRef1())
                    .setRef2(request.getPaymentInformation().getProductDetail().getProductRef2())
                    .setCallFrom(entryIdFromCallFromDeepLink);

            mockGetCommonPaymentConfigByEntityId(entryId);
            mockGetMasterBiller(masterBillerResponse);
            when(cacheService.get(request.getPaymentInformation().getDeepLinkTransactionId(), DeepLinkRequest.class)).thenReturn(deepLinkRequest);

            assertDoesNotThrow(() -> initializationCommonPaymentService.initialCommonPayment(request, headers));

            var captor = ArgumentCaptor.forClass(CommonPaymentDraftCache.class);
            verify(cacheService, times(1)).set(anyString(), anyString(), captor.capture(), anyInt());
            assertNotNull(captor.getValue().getDeepLinkRequest());
            assertEquals(request.getPaymentInformation().getEntryId(), captor.getValue().getPaymentInformation().getEntryId());
        }

        private void mockGetCommonPaymentConfigByEntityId(String entryId) throws TMBCommonException {
            CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig()
                    .setCallbackUrls(Collections.singletonList("intra.ttb.com/callback"))
                    .setExpiryDate(LocalDateTime.parse("2099-12-02 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            when(paymentService.getCommonPaymentConfig(correlationId, entryId)).thenReturn(commonPaymentConfig);
        }

        @Test
        void testRetrieveDeeplinkRequestFromCacheWhenTransDataIsBlankShouldReturnNull() {
            InitializationCommonPaymentRequest req = new InitializationCommonPaymentRequest();
            PaymentInformation paymentInformation = new PaymentInformation();
            paymentInformation.setDeepLinkTransactionId("");
            req.setPaymentInformation(paymentInformation);
            DeepLinkRequest result = ReflectionTestUtils.invokeMethod(initializationCommonPaymentService, "retrieveDeeplinkRequestFromCache", req);
            assertNull(result);
        }

        @Test
        void testRetrieveDeeplinkRequestFromCacheWhenTransDataIsNotBlankShouldValidateAndReturn() throws Exception {
            String deepLinkTransData = "cache-key";
            InitializationCommonPaymentRequest req = new InitializationCommonPaymentRequest();
            PaymentInformation paymentInformation = new PaymentInformation();
            paymentInformation.setDeepLinkTransactionId(deepLinkTransData);
            ProductDetail productDetail = ProductDetail.builder().productRef1("REF1").productRef2("REF2").build();
            paymentInformation.setProductDetail(productDetail);
            req.setPaymentInformation(paymentInformation);
            DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
            deepLinkRequest.setRef1("REF1");
            deepLinkRequest.setRef2("REF2");
            when(cacheService.get(anyString(), eq(DeepLinkRequest.class))).thenReturn(deepLinkRequest);

            DeepLinkRequest result = ReflectionTestUtils.invokeMethod(initializationCommonPaymentService, "retrieveDeeplinkRequestFromCache", req);

            assertObjectAreEquals(deepLinkRequest, result);
        }
    }

    @Nested
    class TransactionFromDeepLinkAndCallFromAutoLoanTest {
        @Test
        void testInitialCommonPaymentWhenAmountIsZeroAndTransactionFromDeeplinkAndCallFromAutoLoanShouldNotSetAmountFromDeeplink() throws JsonProcessingException, TMBCommonException {
            String amountFromDeeplink = "0.00";
            request.getPaymentInformation().setDeepLinkTransactionId("deep_link_transaction_id");
            DeepLinkRequest deepLinkRequest = new DeepLinkRequest()
                    .setAmount(amountFromDeeplink)
                    .setCallFrom(BILLER_CALL_FROM_AUTO_LOAN)
                    .setRef1(request.getPaymentInformation().getProductDetail().getProductRef1())
                    .setRef2(request.getPaymentInformation().getProductDetail().getProductRef2());
            mockCommonPaymentConfigSuccess();
            mockGetMasterBiller(masterBillerResponse);
            when(cacheService.get(request.getPaymentInformation().getDeepLinkTransactionId(), DeepLinkRequest.class)).thenReturn(deepLinkRequest);

            assertDoesNotThrow(() -> initializationCommonPaymentService.initialCommonPayment(request, headers));

            var captor = ArgumentCaptor.forClass(CommonPaymentDraftCache.class);
            verify(cacheService, times(1)).set(anyString(), anyString(), captor.capture(), anyInt());
            BigDecimal expectedAmountFromRequest = request.getPaymentInformation().getAmountDetail().getAmountValue();
            String shouldNotChangeEntityId = request.getPaymentInformation().getEntryId();
            assertEquals(expectedAmountFromRequest, captor.getValue().getPaymentInformation().getAmountDetail().getAmountValue());
            assertEquals(shouldNotChangeEntityId, captor.getValue().getPaymentInformation().getEntryId());
        }
    }

    private InitializationCommonPaymentRequest initialCommonPaymentRequest() {
        return new InitializationCommonPaymentRequest()
                .setPaymentInformation(new PaymentInformation()
                        .setEntryId("entry-id")
                        .setCompCode("comp-code")
                        .setTransactionType(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY)
                        .setProductDetail(new ProductDetail()
                                .setProductRef1("ref1")
                                .setProductRef2("ref2"))
                        .setAmountDetail(new AmountDetail()
                                .setAmountValue(new BigDecimal("1000.00")))
                );
    }

    private void mockCommonPaymentConfigSuccess() throws TMBCommonException {
        when(paymentService.getCommonPaymentConfig(anyString(), anyString())).thenReturn(initialCommonPaymentConfigWithExpireDate("2099-12-02 23:59:59"));
    }

    private static CommonPaymentConfig initialCommonPaymentConfigWithExpireDate(String text) {
        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();
        commonPaymentConfig.setCallbackUrls(Collections.singletonList("intra.ttb.com/callback"));
        commonPaymentConfig.setExpiryDate(LocalDateTime.parse(text, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return commonPaymentConfig;
    }

    private void mockGetMasterBiller(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        when(paymentService.getMasterBillerOrElseThrow(any(), any(), any(), any(), any(Supplier.class))).thenReturn(masterBillerResponse);
    }

    private static <T> void assertObjectAreEquals(T expected, T actual) {
        Assertions.assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expected);
    }

    private MasterBillerResponse initialMasterBiller() {
        return new MasterBillerResponse().setBillerInfo(
                        new BillerInfoResponse()
                                .setBillerGroupType(BILLER_GROUP_TYPE_BILL)
                )
                .setRef1(new ReferenceResponse().setIsMobile(false));
    }

}
