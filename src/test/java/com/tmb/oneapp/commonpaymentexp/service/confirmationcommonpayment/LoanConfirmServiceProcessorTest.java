package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityLoanBillPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccountPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.AdditionalParamCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialLoanBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityLoan;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.Collections;

import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_LOAN;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LoanConfirmServiceProcessorTest {
    @Spy
    @InjectMocks
    private LoanConfirmServiceProcessor loanConfirmServiceProcessor;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private CommonValidateConfirmationService commonValidateConfirmationService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private CustomersTransactionService customersTransactionService;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private NotificationCommonPaymentService notificationCommonPaymentService;
    @Mock
    private BaseConfirmServiceHelper baseConfirmServiceHelper;
    @Mock
    private LoyaltyBizService loyaltyBizService;

    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile customerCrmProfile;
    private OCPBillPaymentResponse ocpBillPaymentResponse;
    private String transactionId;
    private String crmId;
    private String correlationId;
    private String compCode;
    private HttpHeaders headers;
    private ConfirmationCommonPaymentRequest request;


    private static final String CALLBACK_URL = "https://api.example.com/callback";
    private static final String API_KEY = "VGhpc0lzQVZlcnlWZXJ5VmVyeVZlcnlWZXJ5TG9uZ1NlY3JldEtleUZvckhTNTEyQWxnb3JpdGhtQW5kSXRNdXN0QmU1MTJCaXRz";

    @BeforeEach
    void setUp() {
        String ipAddress = "127.0.0.1";

        transactionId = "txn-001";
        crmId = "crm-001";
        correlationId = "corr-001";

        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.set(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, "en");
        headers.set(CommonPaymentExpConstant.HEADER_APP_VERSION, "5.12.0");

        compCode = "AL01";
        request = new ConfirmationCommonPaymentRequest();
        request.setTransactionId(transactionId);

        customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPinFreeSeetingFlag("Y");
        customerCrmProfile.setPinFreeTxnCount(0);

        draftCache = createDraftCache();

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setAccount(new OCPAccount());
        ocpBillPayment.getAccount().setAvailBal("1000.00");
        ocpBillPayment.setFromAccount(new OCPAccountPayment().setAccountId("**********").setAccountType("SDA"));
        ocpBillPayment.setFromAccount(new OCPAccountPayment().setAccountId("**********").setAccountType("SDA"));
        ocpBillPayment.setRef4("100.00");
        ocpBillPayment.setEpayCode("e-pay-code-mandatory-field");

        ocpBillPaymentResponse = new OCPBillPaymentResponse();
        ocpBillPaymentResponse.setData(ocpBillPayment);

        TestUtils.setUpAsyncExecuteMethodAsyncSafelyVoid(asyncHelper);
    }

    @Test
    void testGetProcessorType_ShouldReturnLoanProcessor() {
        String result = loanConfirmServiceProcessor.getProcessorType();

        assertEquals(BILLER_PAYMENT_TMB_LOAN, result);
    }

    @Nested
    class ConfirmTransactionInLoanProcessor {
        @Test
        void testExecuteConfirm_WhenPayByOwner_ShouldReturnResponseSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(true);
            mockGetBasePrepareDataConfirmReturnData(customerCrmProfile);
            mockConfirmOCPBillPaymentReturnData(ocpBillPaymentResponse);

            ConfirmationCommonPaymentResponse response = loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

            assertNotNull(response);
            assertEquals(new BigDecimal("1000.00"), response.getRemainingBalance());
            assertCallValidateServiceHours();
            assertCallValidateAuthentication();
            assertCallValidateTransactionDuplicate();
            assertCallClearDraftCache();
            assertCallClearDepositCache();
            assertCallUpdatePinFreeCount();
            assertCallCallBackConfirmService();
            assertCallWriteActivityLogSuccess();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
            assertCallSentNotification();
            assertShouldNotCallUpdateAccumulateUsage();
            verify(dailyLimitService, never()).updateAccumulateUsage(any(), any(), anyString(), anyString());
        }

        @Test
        void testExecuteConfirm_WhenNotPayByOwner_ShouldReturnResponseSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(false);
            mockGetBasePrepareDataConfirmReturnData(customerCrmProfile);
            mockConfirmOCPBillPaymentReturnData(ocpBillPaymentResponse);

            ConfirmationCommonPaymentResponse response = loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

            assertNotNull(response);
            assertEquals(new BigDecimal("1000.00"), response.getRemainingBalance());
            assertCallValidateServiceHours();
            assertCallValidateAuthentication();
            assertCallValidateTransactionDuplicate();
            assertCallValidateDailyLimitExceeded();
            assertCallClearDraftCache();
            assertCallClearDepositCache();
            assertCallUpdatePinFreeCount();
            AssertCallUpdateAccumulateUsage();
            assertCallCallBackConfirmService();
            assertCallSentNotification();
            assertCallWriteActivityLogSuccess();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
        }

        @Test
        void testExecuteConfirm_WhenFailedConfirmOCPBillPaymentException_ShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(false);
            mockGetBasePrepareDataConfirmReturnData(customerCrmProfile);
            TMBCommonException throwException = CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            when(paymentService.confirmOCPBillPayment(eq(correlationId), eq(crmId), eq(transactionId), any(OCPBillRequest.class))).thenThrow(throwException);
            when(baseConfirmServiceHelper.baseHandleException(eq(request), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
            assertCallWriteActivityLogFailed();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
        }

        @Test
        void testExecuteConfirm_WhenFailedGetBasePrepareDataConfirmException_ShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(false);
            Class<TMBCommonException> exceptionClass = TMBCommonException.class;
            when(baseConfirmServiceHelper.getBasePrepareDataConfirm(eq(headers), anyString())).thenThrow(exceptionClass);
            when(baseConfirmServiceHelper.baseHandleException(eq(request), any(exceptionClass))).thenThrow(exceptionClass);

            assertThrows(TMBCommonException.class, () -> loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertCallWriteActivityLogFailed();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
        }

        @Test
        void testExecuteConfirm_WhenPayWithWow_ShouldReturnResponseSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(true);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);

            WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
            wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
            wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));
            draftCache.getPaymentInformation().setCompCode("AL02");
            draftCache.getCommonPaymentConfig().setCallbackUrls(Collections.singletonList(CALLBACK_URL));
            draftCache.setCommonPaymentRule(commonPaymentRule);
            draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
            draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);

            mockGetBasePrepareDataConfirmReturnData(customerCrmProfile);
            Mockito.doNothing().when(loyaltyBizService).redeemPoint(any(), any());
            when(paymentService.confirmOCPBillWowPointHomeLoanPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

            ConfirmationCommonPaymentResponse response = loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

            assertNotNull(response);
            assertEquals(new BigDecimal("1000.00"), response.getRemainingBalance());
            assertCallValidateServiceHours();
            assertCallValidateAuthentication();
            assertCallValidateTransactionDuplicate();
            assertCallClearDraftCache();
            assertCallClearDepositCache();
            assertCallUpdatePinFreeCount();
            assertCallCallBackConfirmService();
            assertCallWriteActivityLogSuccess();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
            assertCallSentNotification();
            assertShouldNotCallUpdateAccumulateUsage();
            verify(dailyLimitService, never()).validateDailyLimitExceeded(anyString(), any(), any());
        }

        @Test
        void testExecuteConfirm_WhenPayWithWowWithOcpConfirmFail_ShouldThrowRedeemWowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(true);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);

            WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
            wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
            wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));
            draftCache.getPaymentInformation().setCompCode("AL02");
            draftCache.getCommonPaymentConfig().setCallbackUrls(Collections.singletonList(CALLBACK_URL));
            draftCache.setCommonPaymentRule(commonPaymentRule);
            draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
            draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);

            mockGetBasePrepareDataConfirmReturnData(customerCrmProfile);
            Mockito.doNothing().when(loyaltyBizService).redeemPoint(any(), any());

            TMBCommonException throwException = new TMBCommonException(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), "wow_0059 : serial_code is invalid", ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getService(), HttpStatus.OK, null);
            when(paymentService.confirmOCPBillWowPointHomeLoanPayment(any(), any(), any(), any())).thenThrow(throwException);
            when(baseConfirmServiceHelper.baseHandleException(eq(request), any(TMBCommonException.class))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), exception.getErrorCode());
            assertCallWriteActivityLogFailed();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
        }

        @Test
        void testExecuteConfirm_WhenPayWithWowWithRedeemPointFail_ShouldThrowRedeemWowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(true);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);

            WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
            wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
            wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));
            draftCache.getPaymentInformation().setCompCode("AL02");
            draftCache.getCommonPaymentConfig().setCallbackUrls(Collections.singletonList(CALLBACK_URL));
            draftCache.setCommonPaymentRule(commonPaymentRule);
            draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
            draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);

            mockGetBasePrepareDataConfirmReturnData(customerCrmProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), "wow_0059 : serial_code is invalid", ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getService(), HttpStatus.OK, null);
            Mockito.doThrow(throwException).when(loyaltyBizService).redeemPoint(any(), any());
            when(baseConfirmServiceHelper.baseHandleException(eq(request), any(TMBCommonException.class))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), exception.getErrorCode());
            assertCallWriteActivityLogFailed();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
        }

        private void assertCallWriteActivityLogFailed() {
            verify(logEventPublisherService, times(1)).saveActivityLog(any(ActivityLoanBillPayConfirmationEvent.class));
        }

        private void assertCallSentNotification() {
            verify(notificationCommonPaymentService, times(1)).sendENotification(any(NotificationCommonPayment.class));
        }

        private void assertCallWriteTransactionLog() {
            verify(logEventPublisherService, times(1)).saveTransactionLog(eq(correlationId), any(TransactionActivityLoan.class));
        }

        private void assertCallWriteFinancialLog() {
            verify(logEventPublisherService, times(1)).saveFinancialLog(eq(correlationId), any(FinancialLoanBillPayActivityLog.class));
        }

        private void assertCallWriteActivityLogSuccess() {
            verify(logEventPublisherService, times(1)).saveActivityLog(any(ActivityLoanBillPayConfirmationEvent.class), any(ActivityCustomSlipCompleteEvent.class));
        }

        private void assertCallValidateAuthentication() throws TMBCommonException {
            verify(commonValidateConfirmationService, times(1)).verifyAuthentication(transactionId, draftCache, headers);
        }

        private void assertCallCallBackConfirmService() {
            verify(baseConfirmServiceHelper, times(1)).baseExecuteCallbackIfConfiguredAsync(eq(draftCache), anyString(), anyString());
        }

        private void AssertCallUpdateAccumulateUsage() {
            verify(dailyLimitService).updateAccumulateUsage(eq(draftCache), any(CustomerCrmProfile.class), eq(crmId), eq(correlationId));
        }

        private void assertShouldNotCallUpdateAccumulateUsage() {
            verify(dailyLimitService, times(0)).updateAccumulateUsage(any(), any(), anyString(), anyString());
        }

        private void assertCallUpdatePinFreeCount() {
            verify(baseConfirmServiceHelper, times(1)).baseUpdatePinFreeCountWithCondition(anyString(), anyString(), any(), anyBoolean());
        }

        private void assertCallClearDepositCache() {
            verify(customersTransactionService, times(1)).clearDepositCache(any(), any());
        }

        private void assertCallClearDraftCache() {
            verify(baseConfirmServiceHelper, times(1)).baseClearDraftDataCache(anyString());
        }

        private void assertCallValidateDailyLimitExceeded() throws TMBCommonException {
            verify(dailyLimitService, times(1)).validateDailyLimitExceeded(eq(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType()), any(CustomerCrmProfile.class), eq(new BigDecimal(draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getAmount())));
        }

        private void assertCallValidateTransactionDuplicate() throws TMBCommonException {
            verify(commonValidateConfirmationService, times(1)).validateTransactionByTransactionId(transactionId);
        }

        private void assertCallValidateServiceHours() throws TMBCommonException {
            verify(commonValidateConfirmationService, times(1)).validateServiceHours(any());
        }
    }

    @Nested
    class ProcessAfterConfirmWithExternal {
        @Test
        void testProcessAfterConfirmWithExternal_WhenFailedExceptionAfterCallExternalServiceSuccess_ShouldDoesNotThrow() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(true);
            mockGetBasePrepareDataConfirmReturnData(customerCrmProfile);
            mockConfirmOCPBillPaymentReturnData(ocpBillPaymentResponse);
            doThrow(NullPointerException.class).when(loanConfirmServiceProcessor).processAfterConfirmWithExternal(any(), any(), any(), any(), any());

            assertDoesNotThrow(() -> loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache));
        }
    }

    @Test
    void testSaveSuccessLog_WhenFailedExceptionAfterCallExternalServiceSuccess_ShouldDoesNotThrow() throws TMBCommonException, TMBCommonExceptionWithResponse {
        draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(true);
        mockGetBasePrepareDataConfirmReturnData(customerCrmProfile);
        mockConfirmOCPBillPaymentReturnData(ocpBillPaymentResponse);
        doThrow(NullPointerException.class).when(loanConfirmServiceProcessor).saveSuccessLog(any(), any(), any(), any(), any(), any(), any());

        assertDoesNotThrow(() -> loanConfirmServiceProcessor.executeConfirm(request, headers, draftCache));
    }

    private void mockConfirmOCPBillPaymentReturnData(OCPBillPaymentResponse ocpBillPaymentResponse) throws TMBCommonException, TMBCommonExceptionWithResponse {
        when(paymentService.confirmOCPBillPayment(eq(correlationId), eq(crmId), eq(transactionId), any(OCPBillRequest.class))).thenReturn(ocpBillPaymentResponse);
    }

    private void mockGetBasePrepareDataConfirmReturnData(CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        BasePrepareDataConfirm basePrepareDataConfirm = new BasePrepareDataConfirm().setCustomerCrmProfile(customerCrmProfile).setTransactionTime(String.valueOf(System.currentTimeMillis()));
        when(baseConfirmServiceHelper.getBasePrepareDataConfirm(any(HttpHeaders.class), anyString())).thenReturn(basePrepareDataConfirm);
    }

    private CommonPaymentDraftCache createDraftCache() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        OCPBillRequest ocpBillRequest = new OCPBillRequest();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        ocpBillRequest.setAmount("1000.00");
        ocpBillRequest.setEpayCode("epay-001");
        ocpBillRequest.setFee(new OCPFee());
        ocpBillRequest.setFromAccount(new OCPAccountPayment().setAccountId("43210"));
        ocpBillRequest.setAmount("1000");
        ocpBillRequest.setCompCode(compCode);
        ocpBillRequest.setToAccount(new OCPAccountPayment().setAccountId("01234"));
        PaymentInformation paymentInformation = new PaymentInformation();
        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        ProductDetail productDetail = new ProductDetail();

        billerInfo.setBillerGroupType("0");
        billerInfo.setPaymentMethod("5");

        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(new ReferenceResponse());

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setBillerGroupType("0");

        billerInfoResponse.setPaymentMethod("5");
        billerInfoResponse.setNameEn("biller name");
        billerInfoResponse.setBillerCompCode(compCode);

        validateRequest.setNote("test note");

        productDetail.setProductRef1("reference1");
        productDetail.setProductRef2("reference2");

        paymentInformation.setCompCode(compCode);
        paymentInformation.setProductDetail(productDetail);

        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.setRequireCommonAuthen(false);
        validateDraftCache.setFromDepositAccount(new DepositAccountInCache());
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.getMasterBillerResponse().setBillerInfo(CacheMapper.INSTANCE.toBillerInfoResponseInCache(billerInfoResponse));
        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setOcpBillPaymentConfirmRequest(ocpBillRequest));
        validateDraftCache.setFeeCalculated(new BigDecimal("10.50"));
        validateDraftCache.setAdditionalParam(new AdditionalParamCommonPaymentDraftCache());

        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();
        commonPaymentConfig.setCallbackUrls(Collections.singletonList(CALLBACK_URL));

        cache.setValidateDraftCache(validateDraftCache);
        cache.setValidateRequest(validateRequest);
        cache.setPaymentInformation(paymentInformation);

        cache.getValidateRequest().setCreditCard(new CreditCardValidationCommonPaymentRequest());
        cache.getValidateRequest().setDeposit(new DepositValidationCommonPaymentRequest());
        cache.setCommonPaymentConfig(commonPaymentConfig);


        return cache;

    }
}