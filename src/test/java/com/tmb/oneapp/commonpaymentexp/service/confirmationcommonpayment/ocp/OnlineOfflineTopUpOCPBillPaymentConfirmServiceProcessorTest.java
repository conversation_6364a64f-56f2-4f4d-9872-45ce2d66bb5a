package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ocp;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityBillPayOCPConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccountPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialRequest;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.Collections;

import static com.tmb.common.constants.TmbCommonUtilityConstants.ACCOUNT_TYPE_CCA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_METHOD_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_EASY_PASS;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_FLEET_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.PAYMENT_METHOD_TMB_PRODUCT;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.BILLER_GROUP_BILL_PAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OnlineOfflineTopUpOCPBillPaymentConfirmServiceProcessorTest {
    @InjectMocks
    private OnlineOfflineTopUpOCPBillPaymentConfirmServiceProcessor onlineOfflineTopUpBillPaymentConfirmServiceProcessor;
    @Mock
    private CommonValidateConfirmationService commonValidateConfirmationService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private BaseConfirmServiceHelper baseConfirmServiceHelper;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private LoyaltyBizService loyaltyBizService;

    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile customerProfile;
    private OCPBillPaymentResponse ocpBillPaymentResponse;
    private OCPBillPayment ocpBillPayment;
    private String transactionId;
    private String crmId;
    private String correlationId;
    private String creditCardNumber;
    private HttpHeaders headers;
    private ConfirmationCommonPaymentRequest request;
    private BasePrepareDataConfirm basePrepareDataConfirm;

    private static final String CALLBACK_URL = "https://api.example.com/callback";
    private static final String API_KEY = "VGhpc0lzQVZlcnlWZXJ5VmVyeVZlcnlWZXJ5TG9uZ1NlY3JldEtleUZvckhTNTEyQWxnb3JpdGhtQW5kSXRNdXN0QmU1MTJCaXRz";

    @BeforeEach
    void setUp() {
        String ipAddress = "127.0.0.1";

        transactionId = "txn-001";
        crmId = "crm-001";
        correlationId = "corr-001";

        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.set(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, "en");
        headers.set(CommonPaymentExpConstant.HEADER_APP_VERSION, "5.12.0");

        request = new ConfirmationCommonPaymentRequest();
        request.setTransactionId(transactionId);

        basePrepareDataConfirm = new BasePrepareDataConfirm();

        customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(0);
        customerProfile.setAutoSaveSlipMain("Y");

        draftCache = createDraftCache();

        ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setAccount(new OCPAccount());
        ocpBillPayment.getAccount().setAvailBal("1000.00");
        ocpBillPayment.setFromAccount(new OCPAccountPayment().setAccountId("**********").setAccountType("SDA"));
        ocpBillPayment.setFromAccount(new OCPAccountPayment().setAccountId("**********").setAccountType("SDA"));
        ocpBillPayment.setRef4("100.00");
        ocpBillPayment.setEpayCode("ePayCode");

        ocpBillPaymentResponse = new OCPBillPaymentResponse();
        ocpBillPaymentResponse.setData(ocpBillPayment);

        creditCardNumber = "************";

        TestUtils.setUpAsyncExecuteMethodAsyncSafelyVoid(asyncHelper);
    }

    @Test
    void testConfirmWhenSuccessThenReturnResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        draftCache.getPaymentInformation().setCompCode(BILL_COMP_CODE_EASY_PASS);
        draftCache.getCommonPaymentConfig().setCallbackUrls(Collections.singletonList(CALLBACK_URL));
        mockGetBasePrepareDataConfirm(customerProfile);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals(new BigDecimal("100.00"), response.getEasyPass().getBalanceAfterTopUp());
        assertEquals(new BigDecimal("1000.00"), response.getRemainingBalance());
        assertNotNull(response.getQr());
        verify(paymentService, times(1)).confirmOCPBillPayment(eq(correlationId), eq(crmId), eq(transactionId), any());
        verify(commonValidateConfirmationService, times(1)).baseValidateData(eq(request), eq(headers), eq(draftCache), any(BasePrepareDataConfirm.class));
        verify(baseConfirmServiceHelper, times(1)).baseConfirmDataAfterConfirmExternal(request, headers, draftCache, basePrepareDataConfirm);
    }

    @Test
    void testConfirmWhenPaymentServiceFailsTMBCommonExceptionThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        mockGetBasePrepareDataConfirm(customerProfile);
        TMBCommonException throwException = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenThrow(throwException);
        when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenThrow(throwException);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        var captorActivityLog = ArgumentCaptor.forClass(ActivityBillPayOCPConfirmationEvent.class);
        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        verify(logEventPublisherService, times(1)).saveFinancialLog(anyString(), any());
        verify(logEventPublisherService, times(1)).saveTransactionLog(anyString(), any());
        verify(logEventPublisherService, times(1)).saveActivityLog(captorActivityLog.capture());
        assertEquals("100001 : Generic error", captorActivityLog.getValue().getFailReason());
    }

    @Test
    void testConfirmWhenPaymentServiceFailsTMBCommonWithResponseExceptionThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        mockGetBasePrepareDataConfirm(customerProfile);
        TMBCommonExceptionWithResponse throwException = new TMBCommonExceptionWithResponse(ResponseCode.FAILED_V2.getCode(), ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null, null);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenThrow(throwException);
        when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenThrow(throwException);

        TMBCommonExceptionWithResponse exception = assertThrows(TMBCommonExceptionWithResponse.class, () -> onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        var captorActivityLog = ArgumentCaptor.forClass(ActivityBillPayOCPConfirmationEvent.class);
        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        verify(logEventPublisherService, times(1)).saveFinancialLog(anyString(), any());
        verify(logEventPublisherService, times(1)).saveTransactionLog(anyString(), any());
        verify(logEventPublisherService, times(1)).saveActivityLog(captorActivityLog.capture());
        assertEquals("100001 : Generic error", captorActivityLog.getValue().getFailReason());
    }

    @Test
    void testConfirmWhenEasyPassResponseThenIncludeBalanceAfterTopUp() throws TMBCommonException, TMBCommonExceptionWithResponse {
        draftCache.getPaymentInformation().setCompCode(BILL_COMP_CODE_EASY_PASS);
        mockGetBasePrepareDataConfirm(customerProfile);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals(new BigDecimal("100.00"), response.getEasyPass().getBalanceAfterTopUp());
    }

    @Test
    void testGetProcessorTypeThenReturnBillPay() {
        String result = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.getProcessorType();

        assertEquals(BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP, result);
    }

    @Test
    void testConfirmWhenPayWithCreditCardThenSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
        ArgumentCaptor<FinancialRequest> financialRequestArgumentCaptor = ArgumentCaptor.forClass(FinancialRequest.class);
        draftCache = mockDraftCacheWithCreditCard();
        mockGetBasePrepareDataConfirm(customerProfile);

        OCPBillPayment ocpResponse = new OCPBillPayment();
        ocpResponse.setRef4("100.00");
        ocpResponse.setAmount("500.00");
        ocpResponse.setFromAccount(new OCPAccountPayment().setAccountId("43210").setAccountType(ACCOUNT_TYPE_CCA));
        OCPAccount ocpAccount = new OCPAccount();
        ocpAccount.setLedgerBal("100.00");
        ocpResponse.setAccount(ocpAccount);

        OCPBillPaymentResponse ocpBillPaymentResponse = new OCPBillPaymentResponse();
        ocpBillPaymentResponse.setData(ocpResponse);

        when(paymentService.confirmOCPBillPayment(anyString(), anyString(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals("100.00", response.getEasyPass().getBalanceAfterTopUp().toString());

        verify(paymentService).confirmOCPBillPayment(anyString(), anyString(), any(), any());
        verify(baseConfirmServiceHelper, times(1)).baseConfirmDataAfterConfirmExternal(eq(request), eq(headers), eq(draftCache), any(BasePrepareDataConfirm.class));
        verify(logEventPublisherService).saveActivityLog(any(), any());
        verify(logEventPublisherService).saveFinancialLog(anyString(), financialRequestArgumentCaptor.capture());
        verify(logEventPublisherService).saveTransactionLog(anyString(), any());
        verify(commonValidateConfirmationService, times(1)).baseValidateData(eq(request), eq(headers), eq(draftCache), any(BasePrepareDataConfirm.class));

        assertEquals(creditCardNumber, financialRequestArgumentCaptor.getValue().getFromAccNo());
    }

    @Test
    void testConfirmWhenPayWithCreditCardAndPaymentServiceErrorThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        draftCache = mockDraftCacheWithCreditCard();
        CustomerCrmProfile customerProfile = new CustomerCrmProfile();

        mockGetBasePrepareDataConfirm(customerProfile);
        TMBCommonException throwException = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
        when(paymentService.confirmOCPBillPayment(anyString(), anyString(), any(), any())).thenThrow(throwException);
        when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenThrow(throwException);

        assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        verify(logEventPublisherService).saveActivityLog(any());
        verify(logEventPublisherService).saveFinancialLog(anyString(), any());
        verify(logEventPublisherService).saveTransactionLog(anyString(), any());
    }

    @Test
    void testConfirmWhenDailyLimitExceededThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        mockGetBasePrepareDataConfirm(customerProfile);
        TMBCommonException throwException = new TMBCommonException(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), ResponseCode.DAILY_LIMIT_EXCEEDED.getMessage(), ResponseCode.DAILY_LIMIT_EXCEEDED.getService(), HttpStatus.BAD_REQUEST, null);
        doThrow(throwException).when(commonValidateConfirmationService).baseValidateData(any(), any(), any(), any());
        when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenThrow(throwException);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmWhenProcessConfirmDataAfterConfirmExternalFailsThenStillReturnSuccessResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        mockGetBasePrepareDataConfirm(customerProfile);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);
        doThrow(new RuntimeException("Notification failed")).when(baseConfirmServiceHelper).baseConfirmDataAfterConfirmExternal(any(), any(), any(), any());

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals(ocpBillPayment.getAccount().getAvailBal(), response.getRemainingBalance().toString());
    }

    @Test
    void testConfirmWhenTTBProductThenHideMiniQR() throws TMBCommonException, TMBCommonExceptionWithResponse {
        BillerInfoResponseInCache billerInfo = draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo();
        billerInfo.setPaymentMethod(PAYMENT_METHOD_TMB_PRODUCT);
        billerInfo.setBillerMethod(BILLER_METHOD_TMB_LOAN);
        billerInfo.setBillerGroupType(BILLER_GROUP_BILL_PAY);

        mockGetBasePrepareDataConfirm(customerProfile);
        ocpBillPayment.getFromAccount().setAccountType("SDA");
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNull(response.getQr());
        verify(paymentService).confirmOCPBillPayment(eq(correlationId), eq(crmId), eq(transactionId), any());
    }

    @Test
    void testConfirmWhenPayWithCreditCardThenHideMiniQR() throws TMBCommonException, TMBCommonExceptionWithResponse {
        BillerInfoResponseInCache billerInfo = draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo();
        billerInfo.setPaymentMethod("OTHER_METHOD");
        billerInfo.setBillerMethod(BILLER_METHOD_TMB_LOAN);
        billerInfo.setBillerGroupType(BILLER_GROUP_BILL_PAY);
        billerInfo.setBillerCompCode("OTHER_COMP_CODE");

        mockGetBasePrepareDataConfirm(customerProfile);
        ocpBillPayment.getFromAccount().setAccountType(ACCOUNT_TYPE_CCA);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNull(response.getQr());
        verify(paymentService).confirmOCPBillPayment(eq(correlationId), eq(crmId), eq(transactionId), any());
    }

    @Test
    void testConfirmWhenTTBFleetCardThenHideMiniQR() throws TMBCommonException, TMBCommonExceptionWithResponse {
        BillerInfoResponseInCache billerInfo = draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo();
        billerInfo.setPaymentMethod("OTHER_METHOD");
        billerInfo.setBillerMethod(BILLER_METHOD_TMB_LOAN);
        billerInfo.setBillerGroupType(BILLER_GROUP_BILL_PAY);
        billerInfo.setBillerCompCode(BILL_COMP_CODE_TTB_FLEET_CARD);

        mockGetBasePrepareDataConfirm(customerProfile);
        ocpBillPayment.getFromAccount().setAccountType("SDA");
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNull(response.getQr());
        verify(paymentService).confirmOCPBillPayment(eq(correlationId), eq(crmId), eq(transactionId), any());
    }

    @Test
    void testConfirmWhenNotHideMiniQRConditionsThenShowQR() throws TMBCommonException, TMBCommonExceptionWithResponse {
        BillerInfoResponseInCache billerInfo = draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo();
        billerInfo.setPaymentMethod("OTHER_METHOD");
        billerInfo.setBillerMethod(BILLER_METHOD_TMB_LOAN);
        billerInfo.setBillerGroupType(BILLER_GROUP_BILL_PAY);
        billerInfo.setBillerCompCode("OTHER_COMP_CODE");

        mockGetBasePrepareDataConfirm(customerProfile);
        ocpBillPayment.getFromAccount().setAccountType("SDA");
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response.getQr());
        verify(paymentService).confirmOCPBillPayment(eq(correlationId), eq(crmId), eq(transactionId), any());
    }

    @Test
    void testConfirmWhenAvailBalIsNullThenReturnNullRemainingBalance() throws TMBCommonException, TMBCommonExceptionWithResponse {
        mockGetBasePrepareDataConfirm(customerProfile);
        ocpBillPayment.getAccount().setAvailBal(null);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNull(response.getRemainingBalance());
    }

    @Test
    void testConfirmWhenAutoSaveSlipMainNotYThenReturnFalse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        customerProfile.setAutoSaveSlipMain("N");
        mockGetBasePrepareDataConfirm(customerProfile);
        when(paymentService.confirmOCPBillPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

        ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertFalse(response.isAutoSaveSlip());
    }

    @Nested
    class ConfirmWhenPayWithWowTests {
        @Test
        void testConfirmWhenPayWithWowSuccessThenReturnResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);

            WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
            wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
            wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));
            draftCache.getPaymentInformation().setCompCode(BILL_COMP_CODE_EASY_PASS);
            draftCache.getCommonPaymentConfig().setCallbackUrls(Collections.singletonList(CALLBACK_URL));
            draftCache.setCommonPaymentRule(commonPaymentRule);
            draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
            draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);
            mockGetBasePrepareDataConfirm(customerProfile);
            Mockito.doNothing().when(loyaltyBizService).redeemPoint(any(), any());
            when(paymentService.confirmOCPBillWowPointPayment(any(), any(), any(), any())).thenReturn(ocpBillPaymentResponse);

            ConfirmationCommonPaymentResponse response = onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

            assertNotNull(response);
            assertEquals(new BigDecimal("100.00"), response.getEasyPass().getBalanceAfterTopUp());
            assertEquals(new BigDecimal("1000.00"), response.getRemainingBalance());
            assertNotNull(response.getQr());
            verify(paymentService, times(1)).confirmOCPBillWowPointPayment(eq(correlationId), eq(crmId), eq(transactionId), any());
            verify(commonValidateConfirmationService, times(1)).baseValidateData(eq(request), eq(headers), eq(draftCache), any(BasePrepareDataConfirm.class));
            verify(baseConfirmServiceHelper, times(1)).baseConfirmDataAfterConfirmExternal(request, headers, draftCache, basePrepareDataConfirm);
        }

        @Test
        void testConfirmWhenPayWithWowFailsThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);

            WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
            wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
            wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));
            draftCache.getPaymentInformation().setCompCode(BILL_COMP_CODE_EASY_PASS);
            draftCache.getCommonPaymentConfig().setCallbackUrls(Collections.singletonList(CALLBACK_URL));
            draftCache.setCommonPaymentRule(commonPaymentRule);
            draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
            draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);
            mockGetBasePrepareDataConfirm(customerProfile);
            Mockito.doNothing().when(loyaltyBizService).redeemPoint(any(), any());

            TMBCommonException throwException = new TMBCommonException(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), "wow_0059 : serial_code is invalid", ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getService(), HttpStatus.OK, null);
            when(paymentService.confirmOCPBillWowPointPayment(any(), any(), any(), any())).thenThrow(throwException);
            when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), exception.getErrorCode());
            verify(logEventPublisherService, times(1)).saveActivityLog(any());
            verify(logEventPublisherService, times(1)).saveFinancialLog(anyString(), any());
            verify(logEventPublisherService, times(1)).saveTransactionLog(anyString(), any());
        }
    }

    @Nested
    class validateDateTests {

        @Test
        void testValidatePaymentConfirmWhenBaseValidateDataFailsThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirm(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.UNAUTHORIZED, null);
            doThrow(throwException).when(commonValidateConfirmationService).baseValidateData(eq(request), eq(headers), eq(draftCache), any(BasePrepareDataConfirm.class));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    onlineOfflineTopUpBillPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
        }
    }

    private void mockGetBasePrepareDataConfirm(CustomerCrmProfile customerProfile) throws TMBCommonException {
        basePrepareDataConfirm.setCustomerCrmProfile(customerProfile);
        basePrepareDataConfirm.setTransactionTime(String.valueOf(System.currentTimeMillis()));
        when(baseConfirmServiceHelper.getBasePrepareDataConfirm(any(), anyString())).thenReturn(basePrepareDataConfirm);
    }

    private CommonPaymentDraftCache createDraftCache() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        OCPBillRequest ocpBillRequest = new OCPBillRequest();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        ocpBillRequest.setAmount("1000.00");
        ocpBillRequest.setEpayCode("epay-001");
        ocpBillRequest.setFee(new OCPFee());
        ocpBillRequest.setFromAccount(new OCPAccountPayment().setAccountId("43210"));
        ocpBillRequest.setAmount("1000");
        ocpBillRequest.setCompCode("5007");
        ocpBillRequest.setToAccount(new OCPAccountPayment().setAccountId("01234"));
        PaymentInformation paymentInformation = new PaymentInformation();
        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        ProductDetail productDetail = new ProductDetail();

        billerInfo.setBillerGroupType("0");
        billerInfo.setPaymentMethod("5");

        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(new ReferenceResponse());

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setBillerGroupType("0");

        billerInfoResponse.setPaymentMethod("5");
        billerInfoResponse.setNameEn("biller name");
        billerInfoResponse.setBillerCompCode("biller comp code");

        validateRequest.setNote("test note");

        productDetail.setProductRef1("reference1");
        productDetail.setProductRef2("reference2");

        paymentInformation.setCompCode("comp-001");
        paymentInformation.setProductDetail(productDetail);

        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.setRequireCommonAuthen(false);
        validateDraftCache.setFromDepositAccount(new DepositAccountInCache());
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.getMasterBillerResponse().setBillerInfo(CacheMapper.INSTANCE.toBillerInfoResponseInCache(billerInfoResponse));
        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setOcpBillPaymentConfirmRequest(ocpBillRequest));
        validateDraftCache.setFeeCalculated(new BigDecimal("10.50"));

        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();

        cache.setValidateDraftCache(validateDraftCache);
        cache.setValidateRequest(validateRequest);
        cache.setPaymentInformation(paymentInformation);

        cache.getValidateRequest().setCreditCard(new CreditCardValidationCommonPaymentRequest());
        cache.getValidateRequest().setDeposit(new DepositValidationCommonPaymentRequest());
        cache.setCommonPaymentConfig(commonPaymentConfig);


        return cache;

    }

    private CommonPaymentDraftCache mockDraftCacheWithCreditCard() {
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache();

        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductRef1("reference1");
        productDetail.setProductRef2("reference2");

        PaymentInformation paymentInfo = new PaymentInformation();
        paymentInfo.setCompCode(BILL_COMP_CODE_EASY_PASS);
        paymentInfo.setProductDetail(productDetail);
        draftCache.setPaymentInformation(paymentInfo);

        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(true);
        creditCard.setAccountId("*************");
        validateRequest.setCreditCard(creditCard);
        draftCache.setValidateRequest(validateRequest);

        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        CreditCardSupplementary creditCardDetail = new CreditCardSupplementary();
        creditCardDetail.setCardNo(creditCardNumber);
        validateDraftCache.setFromCreditCardDetail(CacheMapper.INSTANCE.toCreditCardSupplementaryInCache(creditCardDetail));

        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        ReferenceResponse ref1 = new ReferenceResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setBillerCompCode(BILL_COMP_CODE_EASY_PASS);
        billerInfo.setBillerGroupType(BILLER_GROUP_BILL_PAY);
        billerInfo.setPaymentMethod(PAYMENT_METHOD_TMB_PRODUCT);
        billerInfo.setBillerMethod(BILLER_METHOD_TMB_LOAN);
        billerInfo.setNameEn("easy pass");
        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(ref1);
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));

        OCPBillRequest ocpRequest = new OCPBillRequest();
        OCPAccountPayment ocpAccountPayment = new OCPAccountPayment();
        ocpRequest.setAmount("500.00");
        ocpRequest.setFromAccount(ocpAccountPayment);
        ocpRequest.setAmount("1000");
        ocpRequest.setEpayCode("epay-001");
        ocpRequest.setFee(new OCPFee());
        ocpRequest.setFromAccount(new OCPAccountPayment().setAccountId("43210"));
        ocpRequest.setAmount("1000");
        ocpRequest.setCompCode(BILL_COMP_CODE_EASY_PASS);
        ocpRequest.setToAccount(new OCPAccountPayment().setAccountId("01234"));
        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setOcpBillPaymentConfirmRequest(ocpRequest));

        validateDraftCache.setFeeCalculated(new BigDecimal("10.50"));

        draftCache.setValidateDraftCache(validateDraftCache);

        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();
        draftCache.setCommonPaymentConfig(commonPaymentConfig);

        return draftCache;
    }
}