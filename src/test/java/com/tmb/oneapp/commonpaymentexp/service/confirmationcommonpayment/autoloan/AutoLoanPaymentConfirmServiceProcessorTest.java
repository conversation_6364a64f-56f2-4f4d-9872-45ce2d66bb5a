package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.autoloan;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableSupplier;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.AutoLoanExternalConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.AutoLoanPrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AutoLoanOCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccountPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AccountBalance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanOCPResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETETransaction;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.AdditionalParamCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequestResponse;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2UpdatePaymentFlagResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_IS_USED;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_AUTO_LOAN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Tests for AutoLoanBillPaymentConfirmServiceProcessor
 * This class tests the confirmation process for auto loan bill payments with different scenarios
 */
@ExtendWith(MockitoExtension.class)
class AutoLoanPaymentConfirmServiceProcessorTest {
    @Spy
    @InjectMocks
    private AutoLoanPaymentConfirmServiceProcessor autoLoanPaymentConfirmServiceProcessor;
    @Mock
    private CommonValidateConfirmationService commonValidateConfirmationService;
    @Mock
    private BaseConfirmServiceHelper baseConfirmServiceHelper;
    @Mock
    private PaymentService paymentService;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private CustomersTransactionService customersTransactionService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private NotificationCommonPaymentService notificationCommonPaymentService;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private CustomerService customerService;
    @Mock
    private CacheService cacheService;
    @Mock
    private AutoLoanDeepLinkHelper autoLoanDeepLinkHelper;
    @Mock
    private LoyaltyBizService loyaltyBizService;

    private String transactionId;
    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile customerProfile;
    private String crmId;
    private String correlationId;
    private String ipAddress;
    private TopUpETEResponse topUpETEResponse;
    private TopUpETETransaction topUpETETransaction;
    private K2AddServiceRequestResponse k2AddServiceResponse;
    private K2UpdatePaymentFlagResponse k2UpdatePaymentFlagResponse;
    private TmbOneServiceResponse<K2AddServiceRequestResponse> tmbAddServiceResponse;
    private TmbOneServiceResponse<K2UpdatePaymentFlagResponse> tmbUpdatePaymentFlagResponse;

    private HttpHeaders headers;
    private ConfirmationCommonPaymentRequest request;
    private AutoLoanPrepareDataConfirm prepareDataConfirm;

    private static final String CALLBACK_URL = "https://api.example.com/callback";
    private static final String API_KEY = "VGhpc0lzQVZlcnlWZXJ5VmVyeVZlcnlWZXJ5TG9uZ1NlY3JldEtleUZvckhTNTEyQWxnb3JpdGhtQW5kSXRNdXN0QmU1MTJCaXRz";

    @BeforeEach
    void setUp() throws TMBCommonException, TMBCommonExceptionWithResponse {
        TestUtils.setUpAsyncExecuteMethodAsyncSafelyVoid(asyncHelper);
        crmId = "crm-test-123";
        correlationId = UUID.randomUUID().toString();
        transactionId = UUID.randomUUID().toString();
        ipAddress = "127.0.0.1";

        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.set(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, "en");
        headers.set(CommonPaymentExpConstant.HEADER_APP_VERSION, "5.12.0");

        request = new ConfirmationCommonPaymentRequest();
        request.setTransactionId(transactionId);

        prepareDataConfirm = new AutoLoanPrepareDataConfirm();

        customerProfile = new CustomerCrmProfile();
        customerProfile.setAutoSaveSlipMain("Y");

        setupDraftCache();
        setupMockResponses();

        setUpAsyncHelperExecuteMethodAsync();
        setUpOptionalMockService();
    }

    /**
     * Creates test draft cache data
     */
    private void setupDraftCache() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        TopUpETEPaymentRequest topUpRequest = new TopUpETEPaymentRequest();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        PaymentInformation paymentInformation = new PaymentInformation();
        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductRef1("A1123456789");
        productDetail.setProductRef2("Ref2");
        productDetail.setProductRef3("Ref3");
        AmountDetail amountDetail = new AmountDetail();
        amountDetail.setAmountValue(new BigDecimal("1000.00"));
        amountDetail.setAmountLabelEn("Amount Label En");
        amountDetail.setAmountLabelTh("Amount Label Th");
        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId_111_222_987654321");
        deepLinkRequest.setAmount("1000.00");

        topUpRequest.setAmount(new BigDecimal("1000.00"));
        topUpRequest.setEpayCode("epay-test-001");
        TopUpAccount fromAccount = new TopUpAccount();
        fromAccount.setAccountId("**********");
        fromAccount.setAccountType("SDA");
        topUpRequest.setFromAccount(fromAccount);
        TopUpAccount toAccount = new TopUpAccount();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("HP");
        topUpRequest.setToAccount(toAccount);
        topUpRequest.setReference1("A1123456789");
        topUpRequest.setReference2("Ref2");
        topUpRequest.setReference3("Ref3");
        topUpRequest.setAutoLoanDetails(new AutoLoanDetail());

        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setBillerGroupType("0");
        billerInfo.setBillerCompCode(BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN);
        billerInfo.setNameEn("Auto Loan Biller");
        billerInfo.setPaymentMethod("5");
        billerInfo.setBillerMethod("AUTO_LOAN_METHOD");
        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(new ReferenceResponse());

        paymentInformation.setCompCode("comp-test-001");
        paymentInformation.setProductDetail(productDetail);
        paymentInformation.setAmountDetail(amountDetail);

        validateRequest.setCreditCard(new CreditCardValidationCommonPaymentRequest());
        validateRequest.getCreditCard().setAccountId("6789********1234");
        validateRequest.setDeposit(new DepositValidationCommonPaymentRequest());
        validateRequest.getDeposit().setAccountNumber("**********");

        paymentInformation.setCompCode("comp-test-001");
        paymentInformation.setProductDetail(productDetail);
        paymentInformation.setAmountDetail(amountDetail);

        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setTopUpETEPaymentRequest(topUpRequest));
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.setFromDepositAccount(new DepositAccountInCache());
        validateDraftCache.getFromDepositAccount().setAccountNumber("**********");
        validateDraftCache.setAdditionalParam(new AdditionalParamCommonPaymentDraftCache().setPayByOwner(true));

        CreditCardSupplementary creditCardDetail = new CreditCardSupplementary();
        creditCardDetail.setProductNameEn("Mock Credit Card Product Name EN");
        creditCardDetail.setProductNameTh("ชื่อผลิตภัณฑ์บัตรเครดิตจำลอง");
        creditCardDetail.setCardNo("4111********1111");
        creditCardDetail.setAccountId("6789********1234");
        validateDraftCache.setFromCreditCardDetail(CacheMapper.INSTANCE.toCreditCardSupplementaryInCache(creditCardDetail));
        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();
        commonPaymentConfig.setCallbackUrls(Collections.singletonList(CALLBACK_URL));

        cache.setCommonPaymentConfig(commonPaymentConfig);
        cache.setValidateDraftCache(validateDraftCache);
        cache.setValidateRequest(validateRequest);
        cache.setPaymentInformation(paymentInformation);
        cache.setDeepLinkRequest(CacheMapper.INSTANCE.toDeepLinkRequestInCache(deepLinkRequest));

        draftCache = cache;
    }

    private void setupMockResponses() {
        topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setPaymentId("pay-id-123");
        topUpETETransaction.setPaymentDateTime("2025-04-01 21:00:00");
        TopUpAccount fromAccount = new TopUpAccount();
        fromAccount.setAccountId("**********");
        fromAccount.setAccountType("SDA");
        AccountBalance balance = new AccountBalance();
        balance.setAvailable("5000.00");
        fromAccount.setBalances(balance);
        topUpETETransaction.setFromAccount(fromAccount);

        topUpETEResponse = new TopUpETEResponse();
        topUpETEResponse.setTransaction(topUpETETransaction);

        k2AddServiceResponse = new K2AddServiceRequestResponse();
        k2AddServiceResponse.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);
        k2AddServiceResponse.setSrId("sr-id-111");
        k2AddServiceResponse.setSrNo("sr-no-222");

        k2UpdatePaymentFlagResponse = new K2UpdatePaymentFlagResponse();
        k2UpdatePaymentFlagResponse.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);

        tmbAddServiceResponse = new TmbOneServiceResponse<>();
        tmbAddServiceResponse.setData(k2AddServiceResponse);

        tmbUpdatePaymentFlagResponse = new TmbOneServiceResponse<>();
        tmbUpdatePaymentFlagResponse.setData(k2UpdatePaymentFlagResponse);
    }

    /**
     * Setup async helper mock behavior
     */
    private void setUpAsyncHelperExecuteMethodAsync() throws TMBCommonException {
        lenient().when(asyncHelper.executeMethodAsync(any())).thenAnswer(invocation -> {
            ThrowableSupplier<?> supplier = invocation.getArgument(0);
            try {
                supplier.get();
                return CompletableFuture.completedFuture(null);
            } catch (Exception e) {
                CompletableFuture<Object> future = new CompletableFuture<>();
                future.completeExceptionally(e);
                return future;
            }
        });
    }

    private void setUpOptionalMockService() throws TMBCommonException, TMBCommonExceptionWithResponse {
        lenient().when(paymentService.confirmAutoLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(TopUpETEPaymentRequest.class)))
                .thenReturn(topUpETEResponse);
        lenient().when(customersTransactionService.clearDepositCache(eq(correlationId), eq(crmId)))
                .thenReturn(true);
        lenient().when(autoLoanDeepLinkHelper.handleServiceRequest(any(DeepLinkRequestInCache.class), any(HttpHeaders.class), anyString(), any(CommonPaymentDraftCache.class)))
                .thenReturn(tmbAddServiceResponse.getData());
        lenient().when(customerService.getCustomerCrmProfile(correlationId, crmId)).thenReturn(customerProfile);
        lenient().doNothing().when(autoLoanDeepLinkHelper).handleUpdatePaymentStatus(anyMap(), any(HttpHeaders.class), any(DeepLinkRequestInCache.class), any(AutoLoanExternalConfirmResponse.class), any(K2AddServiceRequestResponse.class), any(CommonPaymentDraftCache.class));

        lenient().when(asyncHelper.executeRequestAsyncSafely(any())).thenAnswer(invocation -> {
            Supplier<?> supplier = invocation.getArgument(0);
            try {
                Object result = supplier.get();
                return CompletableFuture.completedFuture(result);
            } catch (Exception e) {
                CompletableFuture<Object> future = new CompletableFuture<>();
                future.completeExceptionally(e);
                return future;
            }
        });

        lenient().doNothing().when(dailyLimitService).validateDailyLimitExceeded(any(), any(), any());
        lenient().doNothing().when(dailyLimitService).updateAccumulateUsage(any(), any(), any(), any());
        lenient().doNothing().when(logEventPublisherService).saveActivityLog(any());
        lenient().doNothing().when(logEventPublisherService).saveFinancialLog(anyString(), any());
        lenient().doNothing().when(logEventPublisherService).saveTransactionLog(anyString(), any());
        lenient().doNothing().when(notificationCommonPaymentService).sendENotification(any());
    }

    @Test
    void testGetProcessorTypeWhenCalledThenReturnBillPaymentTypeAutoLoan() {
        assertEquals(BILLER_PAYMENT_TMB_AUTO_LOAN, autoLoanPaymentConfirmServiceProcessor.getProcessorType());
    }

    @Test
    void testConfirmWhenSuccessThenReturnConfirmationResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(false);
        when(paymentService.confirmAutoLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(TopUpETEPaymentRequest.class)))
                .thenReturn(topUpETEResponse);

        ConfirmationCommonPaymentResponse response = autoLoanPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals(new BigDecimal(topUpETETransaction.getFromAccount().getBalances().getAvailable()).setScale(2, RoundingMode.HALF_DOWN),
                response.getRemainingBalance());
        assertEquals(Boolean.TRUE, response.isAutoSaveSlip());
        verify(customersTransactionService).clearDepositCache(eq(correlationId), eq(crmId));
        verify(dailyLimitService).validateDailyLimitExceeded(any(), any(), any());
        verify(commonValidateConfirmationService).validateServiceHours(draftCache);
        verify(commonValidateConfirmationService).verifyAuthentication(transactionId, draftCache, headers);
        verify(commonValidateConfirmationService).validateTransactionByTransactionId(transactionId);
    }

    @Test
    void testConfirmWhenSuccessIsPayByOwnerShouldNotCheckValidateDailyLimit() throws TMBCommonException, TMBCommonExceptionWithResponse {
        when(paymentService.confirmAutoLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(TopUpETEPaymentRequest.class)))
                .thenReturn(topUpETEResponse);

        ConfirmationCommonPaymentResponse response = autoLoanPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals(new BigDecimal(topUpETETransaction.getFromAccount().getBalances().getAvailable()).setScale(2, RoundingMode.HALF_DOWN),
                response.getRemainingBalance());
        assertEquals(Boolean.TRUE, response.isAutoSaveSlip());
        verify(customersTransactionService).clearDepositCache(eq(correlationId), eq(crmId));
        verify(dailyLimitService, never()).validateDailyLimitExceeded(any(), any(), any());
        verify(commonValidateConfirmationService).validateServiceHours(draftCache);
        verify(commonValidateConfirmationService).verifyAuthentication(transactionId, draftCache, headers);
        verify(commonValidateConfirmationService).validateTransactionByTransactionId(transactionId);
    }

    @Test
    void testConfirmWhenPaymentServiceThrowsExceptionThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        draftCache.setDeepLinkRequest(null);

        when(paymentService.confirmAutoLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(TopUpETEPaymentRequest.class)))
                .thenThrow(CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.ETE_ERROR));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> autoLoanPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        verify(customersTransactionService, never()).clearDepositCache(eq(correlationId), eq(crmId));
        verify(cacheService, times(1)).delete(anyString(), eq(COMMON_PAYMENT_HASH_KEY_IS_USED));

        verify(autoLoanDeepLinkHelper, never()).handleUpdatePaymentStatus(anyMap(), any(), any(), any(), any(), any());
    }

    @Test
    void testConfirmWhenFailedPaymentServiceThrowsAndTransactionFromDeepLinkShouldUpdateStatusAndThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        when(paymentService.confirmAutoLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(TopUpETEPaymentRequest.class)))
                .thenThrow(CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.ETE_ERROR));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> autoLoanPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        verify(customersTransactionService, never()).clearDepositCache(eq(correlationId), eq(crmId));
        verify(cacheService, times(1)).delete(anyString(), eq(COMMON_PAYMENT_HASH_KEY_IS_USED));
        verify(autoLoanDeepLinkHelper, times(1)).handleUpdatePaymentStatus(anyMap(), any(), any(), any(), any(), any());
    }

    @Test
    void testConfirmWhenFailedNullPointerExceptionShouldThrowTmbCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        when(paymentService.confirmAutoLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(TopUpETEPaymentRequest.class)))
                .thenThrow(NullPointerException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> autoLoanPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        verify(customersTransactionService, never()).clearDepositCache(eq(correlationId), eq(crmId));
        verify(cacheService, times(1)).delete(anyString(), eq(COMMON_PAYMENT_HASH_KEY_IS_USED));
        verify(autoLoanDeepLinkHelper, times(1)).handleUpdatePaymentStatus(anyMap(), any(), any(), any(), any(), any());
    }

    @Test
    void testPerformPostPaymentOperationsWhenNotDeepLinkThenNotCallHandleServiceRequest() {
        draftCache.setDeepLinkRequest(null);
        prepareDataConfirm.setTransactionTime(String.valueOf(System.currentTimeMillis()));
        prepareDataConfirm.setCustomerCrmProfile(new CustomerCrmProfile());
        prepareDataConfirm.setK2AddServiceResponse(new K2AddServiceRequestResponse());

        autoLoanPaymentConfirmServiceProcessor.processAfterConfirmWithExternal(request, headers, draftCache, prepareDataConfirm, new AutoLoanExternalConfirmResponse());

        verify(autoLoanDeepLinkHelper, never()).handleServiceRequest(any(DeepLinkRequestInCache.class), any(HttpHeaders.class), anyString(), any(CommonPaymentDraftCache.class));
        verify(autoLoanDeepLinkHelper, never()).handleUpdatePaymentStatus(anyMap(), any(), any(), any(), any(), any());
    }


    @Test
    void testConfirmWowTransactionWhenSuccessThenReturnConfirmationResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
        commonPaymentRule.setWowPointFlag(true);

        WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
        wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
        wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));
        draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(false);
        draftCache.getPaymentInformation().setCompCode("5003");
        draftCache.getCommonPaymentConfig().setCallbackUrls(Collections.singletonList(CALLBACK_URL));
        draftCache.setCommonPaymentRule(commonPaymentRule);
        draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
        draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);

        AutoLoanOCPBillRequest autoLoanOCPBillRequest = new AutoLoanOCPBillRequest();
        autoLoanOCPBillRequest.setAmount("1000.00");
        autoLoanOCPBillRequest.setEpayCode("epay-001");
        autoLoanOCPBillRequest.setFee(new OCPFee());
        autoLoanOCPBillRequest.setFromAccount(new OCPAccountPayment().setAccountId("43210"));
        autoLoanOCPBillRequest.setAmount("1000");
        autoLoanOCPBillRequest.setCompCode("5003");
        autoLoanOCPBillRequest.setToAccount(new OCPAccountPayment().setAccountId("01234"));
        autoLoanOCPBillRequest.setAutoLoan(new AutoLoanDetail());
        draftCache.getValidateDraftCache().setExternalConfirmRequest(new ExternalConfirmRequest().setAutoLoanOCPBillPaymentConfirmRequest(autoLoanOCPBillRequest));

        AutoLoanOCPResponse autoLoanOCPResponse = new AutoLoanOCPResponse();
        autoLoanOCPResponse.setAccount(new OCPAccount());
        autoLoanOCPResponse.getAccount().setAvailBal("1000.00");
        autoLoanOCPResponse.setFromAccount(new OCPAccountPayment().setAccountId("**********").setAccountType("SDA"));
        autoLoanOCPResponse.setFromAccount(new OCPAccountPayment().setAccountId("**********").setAccountType("SDA"));
        autoLoanOCPResponse.setRef4("100.00");
        autoLoanOCPResponse.setEpayCode("e-pay-code-mandatory-field");


        Mockito.doNothing().when(loyaltyBizService).redeemPoint(any(), any());
        when(paymentService.confirmOCPBillWowPointAutoLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(AutoLoanOCPBillRequest.class)))
                .thenReturn(autoLoanOCPResponse);

        ConfirmationCommonPaymentResponse response = autoLoanPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

        assertNotNull(response);
        assertEquals(new BigDecimal("1000.00"), response.getRemainingBalance());
        assertEquals(Boolean.TRUE, response.isAutoSaveSlip());
        verify(customersTransactionService).clearDepositCache(eq(correlationId), eq(crmId));
        verify(dailyLimitService).validateDailyLimitExceeded(any(), any(), any());
        verify(commonValidateConfirmationService).validateServiceHours(draftCache);
        verify(commonValidateConfirmationService).verifyAuthentication(transactionId, draftCache, headers);
        verify(commonValidateConfirmationService).validateTransactionByTransactionId(transactionId);
    }

    @Test
    void testConfirmWowWhenFailedPaymentServiceThrowsAndTransactionFromDeepLinkShouldUpdateStatusAndThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
        commonPaymentRule.setWowPointFlag(true);

        WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
        wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
        wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));
        draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(false);
        draftCache.getPaymentInformation().setCompCode("5003");
        draftCache.getCommonPaymentConfig().setCallbackUrls(Collections.singletonList(CALLBACK_URL));
        draftCache.setCommonPaymentRule(commonPaymentRule);
        draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
        draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);

        AutoLoanOCPBillRequest autoLoanOCPBillRequest = new AutoLoanOCPBillRequest();
        autoLoanOCPBillRequest.setAmount("1000.00");
        autoLoanOCPBillRequest.setEpayCode("epay-001");
        autoLoanOCPBillRequest.setFee(new OCPFee());
        autoLoanOCPBillRequest.setFromAccount(new OCPAccountPayment().setAccountId("43210"));
        autoLoanOCPBillRequest.setAmount("1000");
        autoLoanOCPBillRequest.setCompCode("5003");
        autoLoanOCPBillRequest.setToAccount(new OCPAccountPayment().setAccountId("01234"));
        autoLoanOCPBillRequest.setAutoLoan(new AutoLoanDetail());
        draftCache.getValidateDraftCache().setExternalConfirmRequest(new ExternalConfirmRequest().setAutoLoanOCPBillPaymentConfirmRequest(autoLoanOCPBillRequest));

        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setAccount(new OCPAccount());
        ocpBillPayment.getAccount().setAvailBal("1000.00");
        ocpBillPayment.setFromAccount(new OCPAccountPayment().setAccountId("**********").setAccountType("SDA"));
        ocpBillPayment.setFromAccount(new OCPAccountPayment().setAccountId("**********").setAccountType("SDA"));
        ocpBillPayment.setRef4("100.00");
        ocpBillPayment.setEpayCode("e-pay-code-mandatory-field");

        Mockito.doNothing().when(loyaltyBizService).redeemPoint(any(), any());
        when(paymentService.confirmOCPBillWowPointAutoLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(AutoLoanOCPBillRequest.class)))
                .thenThrow(CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.ETE_ERROR));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> autoLoanPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

        assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        verify(customersTransactionService, never()).clearDepositCache(eq(correlationId), eq(crmId));
        verify(cacheService, times(1)).delete(anyString(), eq(COMMON_PAYMENT_HASH_KEY_IS_USED));
        verify(autoLoanDeepLinkHelper, times(1)).handleUpdatePaymentStatus(anyMap(), any(), any(), any(), any(), any());
    }
}
