package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Balance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Receiver;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Sender;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Terminal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Collections;

import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_BILL_PROMPT_PAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillPromptPayConfirmServiceProcessorTest {
    @InjectMocks
    private BillPromptPayConfirmServiceProcessor billPromptPayConfirmServiceProcessor;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CustomersTransactionService customersTransactionService;
    @Mock
    private NotificationCommonPaymentService notificationCommonPaymentService;
    @Mock
    private CommonValidateConfirmationService commonValidateConfirmationService;
    @Mock
    private BaseConfirmServiceHelper baseConfirmServiceHelper;
    @Mock
    private BaseBillPayValidator baseBillPayValidator;
    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile customerProfile;
    private PromptPayETEConfirmResponse eteResponse;
    private String transactionId;
    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private ConfirmationCommonPaymentRequest request;

    @BeforeEach
    void setUp() {
        TestUtils.setUpAsyncExecuteMethodAsyncSafelyVoid(asyncHelper);
        ReflectionTestUtils.setField(billPromptPayConfirmServiceProcessor, "isQRISO20022FlagOn", false);

        String ipAddress = "127.0.0.1";

        transactionId = "txn-001";
        crmId = "crm-001";
        correlationId = "corr-001";

        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.set(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, "en");
        headers.set(CommonPaymentExpConstant.HEADER_APP_VERSION, "5.12.0");

        request = new ConfirmationCommonPaymentRequest();
        request.setTransactionId(transactionId);

        customerProfile = new CustomerCrmProfile();
        customerProfile.setPinFreeSeetingFlag("Y");
        customerProfile.setPinFreeTxnCount(0);

        draftCache = createDraftCache();

        eteResponse = new PromptPayETEConfirmResponse();
        eteResponse.setBalance(new Balance());
        eteResponse.getBalance().setAvailable(new BigDecimal("1000.00"));
    }

    @Test
    void testGetProcessorTypeThenReturnBillPromptPay() {
        String result = billPromptPayConfirmServiceProcessor.getProcessorType();

        assertEquals(BILLER_PAYMENT_BILL_PROMPT_PAY, result);
    }

    @Nested
    class TestExecuteConfirmBillPromptPay {
        @Test
        void testExecuteConfirmBillPromptPayWhenSuccessThenReturnResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            when(paymentService.confirmBillPromptPayPayment(any(), any(), any(), any())).thenReturn(eteResponse);

            ConfirmationCommonPaymentResponse actual = billPromptPayConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

            assertNotNull(actual);
            assertEquals(draftCache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest().getTransactionReference(), actual.getReferenceNo());
            assertEquals(new BigDecimal("1000.00"), actual.getRemainingBalance());
            assertNotNull(actual.getQr());
            verify(dailyLimitService, times(1)).validateDailyLimitExceeded(eq(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType()), eq(customerProfile), eq(draftCache.getValidateRequest().getDeposit().getAmount()));
            verify(dailyLimitService, times(1)).updateAccumulateUsage(eq(draftCache), eq(customerProfile), eq(crmId), eq(correlationId));
            verify(baseConfirmServiceHelper, times(1)).baseUpdatePinFreeCountWithCondition(eq(crmId), eq(correlationId), eq(customerProfile), eq(draftCache.getValidateDraftCache().isRequireCommonAuthen()));
            verify(dailyLimitService, times(1)).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
            verify(customersTransactionService, times(1)).clearDepositCache(correlationId, crmId);
            verify(baseConfirmServiceHelper, times(1)).baseClearDraftDataCache(transactionId);
            verify(commonValidateConfirmationService, times(1)).verifyAuthentication(transactionId, draftCache, headers);
            verify(commonValidateConfirmationService, times(1)).validateTransactionByTransactionId(transactionId);
            verify(baseBillPayValidator, times(1)).validateServiceHours(draftCache.getValidateDraftCache().getMasterBillerResponse());
            assertCallWriteActivityLogSuccess();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
            assertCallSentNotification();
        }

        @Test
        void testExecuteConfirmBillPromptPayWhenFailedGotConfirmBillPromptPayPaymentExceptionShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            when(paymentService.confirmBillPromptPayPayment(any(), any(), any(), any())).thenThrow(new TMBCommonException(ResponseCode.FAILED_V2.getCode(), ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(TMBCommonException.class))).thenThrow(TMBCommonException.class);

            assertThrows(TMBCommonException.class, () -> billPromptPayConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertCallWriteActivityLogFailed();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
            verify(baseConfirmServiceHelper, times(1)).baseHandleException(any(), any(TMBCommonException.class));
        }
    }

    @Nested
    class TestExecuteConfirmBillPromptPayISO20022 {
        @Test
        void testExecuteConfirmBillPromptPayWhenIsQRISO20022FlagOnSuccessThenReturnResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
            ReflectionTestUtils.setField(billPromptPayConfirmServiceProcessor, "isQRISO20022FlagOn", true);
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            when(paymentService.confirmBillPromptPayISO20022Payment(any(), any(), any(), any())).thenReturn(eteResponse);

            ConfirmationCommonPaymentResponse actual = billPromptPayConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

            assertNotNull(actual);
            assertEquals(draftCache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest().getTransactionReference(), actual.getReferenceNo());
            assertEquals(new BigDecimal("1000.00"), actual.getRemainingBalance());
            assertNotNull(actual.getQr());
            verify(dailyLimitService, times(1)).validateDailyLimitExceeded(eq(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType()), eq(customerProfile), eq(draftCache.getValidateRequest().getDeposit().getAmount()));
            verify(dailyLimitService, times(1)).updateAccumulateUsage(eq(draftCache), eq(customerProfile), eq(crmId), eq(correlationId));
            verify(baseConfirmServiceHelper, times(1)).baseUpdatePinFreeCountWithCondition(eq(crmId), eq(correlationId), eq(customerProfile), eq(draftCache.getValidateDraftCache().isRequireCommonAuthen()));
            verify(dailyLimitService, times(1)).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
            verify(customersTransactionService, times(1)).clearDepositCache(correlationId, crmId);
            verify(baseConfirmServiceHelper, times(1)).baseClearDraftDataCache(transactionId);
            verify(commonValidateConfirmationService, times(1)).verifyAuthentication(transactionId, draftCache, headers);
            verify(commonValidateConfirmationService, times(1)).validateTransactionByTransactionId(transactionId);
            verify(baseBillPayValidator, times(1)).validateServiceHours(draftCache.getValidateDraftCache().getMasterBillerResponse());
            assertCallWriteActivityLogSuccess();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
            assertCallSentNotification();
        }

        @Test
        void testExecuteConfirmBillPromptPayWhenIsQRISO20022FlagOnAndFailedGotConfirmBillPromptPayPaymentExceptionShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            ReflectionTestUtils.setField(billPromptPayConfirmServiceProcessor, "isQRISO20022FlagOn", true);
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            when(paymentService.confirmBillPromptPayISO20022Payment(any(), any(), any(), any())).thenThrow(new TMBCommonException(ResponseCode.FAILED_V2.getCode(), ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(TMBCommonException.class))).thenThrow(TMBCommonException.class);

            assertThrows(TMBCommonException.class, () -> billPromptPayConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertCallWriteActivityLogFailed();
            assertCallWriteFinancialLog();
            assertCallWriteTransactionLog();
            verify(baseConfirmServiceHelper, times(1)).baseHandleException(any(), any(TMBCommonException.class));
        }
    }

    @Nested
    class validateDateTests {
        @Test
        void testValidatePaymentConfirmWhenValidateServiceHoursFailsShouldThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException("error-code-from-service-failed", ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.UNAUTHORIZED, null);
            doThrow(throwException).when(baseBillPayValidator).validateServiceHours(draftCache.getValidateDraftCache().getMasterBillerResponse());
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    billPromptPayConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals("error-code-from-service-failed", exception.getErrorCode());
        }

        @Test
        void testValidatePaymentConfirmWhenAuthenticationFailsShouldThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.UNAUTHORIZED, null);
            doThrow(throwException).when(commonValidateConfirmationService).verifyAuthentication(eq(transactionId), eq(draftCache), eq(headers));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    billPromptPayConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
            verify(baseBillPayValidator, times(1)).validateServiceHours(draftCache.getValidateDraftCache().getMasterBillerResponse());
        }

        @Test
        void testValidatePaymentConfirmWhenTransactionValidationFailsShouldThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.TRANSACTION_DUPLICATE_ERROR.getCode(), ResponseCode.TRANSACTION_DUPLICATE_ERROR.getMessage(), ResponseCode.TRANSACTION_DUPLICATE_ERROR.getService(), HttpStatus.BAD_REQUEST, null);
            doThrow(throwException).when(commonValidateConfirmationService).validateTransactionByTransactionId(eq(transactionId));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    billPromptPayConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.TRANSACTION_DUPLICATE_ERROR.getCode(), exception.getErrorCode());
            verify(baseBillPayValidator, times(1)).validateServiceHours(draftCache.getValidateDraftCache().getMasterBillerResponse());
            verify(commonValidateConfirmationService, times(1)).verifyAuthentication(transactionId, draftCache, headers);
        }

        @Test
        void testValidatePaymentConfirmWhenValidateDailyLimitExceededExceptionShouldThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            BigDecimal amountFromValidateRequest = draftCache.getValidateRequest().getDeposit().getAmount();
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.TRANSACTION_DUPLICATE_ERROR.getCode(), ResponseCode.TRANSACTION_DUPLICATE_ERROR.getMessage(), ResponseCode.TRANSACTION_DUPLICATE_ERROR.getService(), HttpStatus.BAD_REQUEST, null);
            doThrow(throwException).when(dailyLimitService).validateDailyLimitExceeded(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType(), customerProfile, amountFromValidateRequest);
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    billPromptPayConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.TRANSACTION_DUPLICATE_ERROR.getCode(), exception.getErrorCode());
            verify(baseBillPayValidator, times(1)).validateServiceHours(draftCache.getValidateDraftCache().getMasterBillerResponse());
            verify(commonValidateConfirmationService, times(1)).verifyAuthentication(transactionId, draftCache, headers);
            verify(commonValidateConfirmationService, times(1)).validateTransactionByTransactionId(eq(transactionId));
        }
    }

    private void mockGetBasePrepareDataConfirmReturnData(CustomerCrmProfile customerProfile) throws TMBCommonException {
        BasePrepareDataConfirm basePrepareDataConfirm = new BasePrepareDataConfirm().setCustomerCrmProfile(customerProfile).setTransactionTime(String.valueOf(System.currentTimeMillis()));
        when(baseConfirmServiceHelper.getBasePrepareDataConfirm(any(HttpHeaders.class), anyString())).thenReturn(basePrepareDataConfirm);
    }

    private CommonPaymentDraftCache createDraftCache() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        PaymentInformation paymentInformation = new PaymentInformation();
        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        ProductDetail productDetail = new ProductDetail();

        BigDecimal amount = new BigDecimal("100");
        PromptPayETEConfirmRequest promptPayETEConfirmRequest = new PromptPayETEConfirmRequest();
        promptPayETEConfirmRequest.setAmount(amount);
        promptPayETEConfirmRequest.setTransactionReference("transaction-reference");
        promptPayETEConfirmRequest.setFee(new BigDecimal("100.50"));
        promptPayETEConfirmRequest.setTerminal(new Terminal().setId("terminal-id"));
        promptPayETEConfirmRequest.setReceiver(new Receiver()
                .setAccountDisplayName("account-display-name")
                .setProxyValue("proxy-value")
                .setProxyType("proxy-type")
                .setBankCode("bank-code")
                .setAccountId("account-id"));
        promptPayETEConfirmRequest.setSender(new Sender()
                .setAccountId("sender-account-id")
                .setAccountType("sender-account-type")
                .setAccountName("sender-account-name"));

        billerInfo.setBillerGroupType("0");
        billerInfo.setPaymentMethod("5");

        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(new ReferenceResponse());

        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        billerInfoResponse.setBillerGroupType("0");

        billerInfoResponse.setPaymentMethod("5");
        billerInfoResponse.setNameEn("biller name");
        billerInfoResponse.setBillerCompCode("biller comp code");

        validateRequest.setNote("test note");

        productDetail.setProductRef1("reference1");
        productDetail.setProductRef2("reference2");

        String compCodeBillPromptPay = "***************";
        paymentInformation.setCompCode(compCodeBillPromptPay);
        paymentInformation.setProductDetail(productDetail);

        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setPromptPayConfirmRequest(promptPayETEConfirmRequest));
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.setRequireCommonAuthen(false);
        validateDraftCache.setFromDepositAccount(new DepositAccountInCache());
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.getMasterBillerResponse().setBillerInfo(CacheMapper.INSTANCE.toBillerInfoResponseInCache(billerInfoResponse));
        validateDraftCache.setFeeCalculated(new BigDecimal("10.50"));

        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig()
                .setCallbackUrls(Collections.singletonList("https://api.example.com/callback"));

        cache.setValidateDraftCache(validateDraftCache);
        cache.setValidateRequest(validateRequest);
        cache.setPaymentInformation(paymentInformation);

        cache.getValidateRequest().setCreditCard(new CreditCardValidationCommonPaymentRequest());
        cache.getValidateRequest().setDeposit(new DepositValidationCommonPaymentRequest()
                .setAmount(amount));
        cache.setCommonPaymentConfig(commonPaymentConfig);

        return cache;
    }

    private void assertCallSentNotification() {
        verify(notificationCommonPaymentService, times(1)).sendENotification(any(NotificationCommonPayment.class));
    }

    private void assertCallWriteTransactionLog() {
        verify(logEventPublisherService, times(1)).saveTransactionLog(eq(correlationId), any());
    }

    private void assertCallWriteFinancialLog() {
        verify(logEventPublisherService, times(1)).saveFinancialLog(eq(correlationId), any());
    }

    private void assertCallWriteActivityLogSuccess() {
        verify(logEventPublisherService, times(1)).saveActivityLog(any(), any());
    }

    private void assertCallWriteActivityLogFailed() {
        verify(logEventPublisherService, times(1)).saveActivityLog(any());
    }
}