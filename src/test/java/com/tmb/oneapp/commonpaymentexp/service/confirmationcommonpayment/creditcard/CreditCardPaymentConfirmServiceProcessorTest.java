package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.creditcard;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCreditCardBillPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityLogConfirmMapper;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.CreditCardLogsConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AccountBalance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.creditcard.CreditCardConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.AdditionalParamCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.BillPaymentCreditCard;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.PayeeCard;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.PayerAccount;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseConfirmLogRecord;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialCreditCardBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialLogMapper;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialRequest;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityCreditCardBillPay;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionLogMapper;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CreditCardService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerExpService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.UUID;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialLogMapper.ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreditCardPaymentConfirmServiceProcessorTest {
    @Mock
    private CommonValidateConfirmationService commonValidateConfirmationService;
    @Mock
    private PaymentService paymentService;
    @Mock
    private BaseConfirmServiceHelper baseConfirmServiceHelper;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CustomerExpService customerExpService;
    @Mock
    private CreditCardService creditCardService;
    @Mock
    private CustomersTransactionService customersTransactionService;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private NotificationCommonPaymentService notificationCommonPaymentService;

    @Spy
    @InjectMocks
    private CreditCardPaymentConfirmServiceProcessor creditCardPaymentConfirmServiceProcessor;

    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile customerProfile;
    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private ConfirmationCommonPaymentRequest request;
    private BasePrepareDataConfirm basePrepareDataConfirm;
    private CreditCardConfirmResponse creditCardConfirmResponse;
    private static final String CALLBACK_URL = "https://api.example.com/callback";
    private static final String API_KEY = "VGhpc0lzQVZlcnlWZXJ5VmVyeVZlcnlWZXJ5TG9uZ1NlY3JldEtleUZvckhTNTEyQWxnb3JpdGhtQW5kSXRNdXN0QmU1MTJCaXRz";

    @BeforeEach
    void setUp() {
        TestUtils.setUpAsyncExecuteMethodAsyncSafelyVoid(asyncHelper);

        crmId = "crm-test-123";
        correlationId = UUID.randomUUID().toString();
        String transactionId = UUID.randomUUID().toString();
        String ipAddress = "127.0.0.1";

        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.set(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, "en");
        headers.set(CommonPaymentExpConstant.HEADER_APP_VERSION, "5.12.0");

        request = new ConfirmationCommonPaymentRequest();
        request.setTransactionId(transactionId);

        basePrepareDataConfirm = new BasePrepareDataConfirm();

        customerProfile = new CustomerCrmProfile();
        customerProfile.setAutoSaveSlipMain("Y");

        draftCache = createDraftCache();
        creditCardConfirmResponse = createCreditCardConfirmResponse();
    }

    @Test
    void testGetProcessorTypeWhenCalledThenReturnBillerPaymentCreditCard() {
        assertEquals(BILLER_PAYMENT_CREDIT_CARD, creditCardPaymentConfirmServiceProcessor.getProcessorType());
    }

    @Nested
    class ConfirmCreditCardPaymentTest {
        @Test
        void testConfirmCreditCardPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            setupPaymentServiceMocks();

            ConfirmationCommonPaymentResponse response = creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);
            assertNotNull(response);
            assertNotNull(response.getRemainingBalance());
            assertEquals("5000.00", response.getRemainingBalance().toString());
            assertEquals(draftCache.getPaymentInformation().getCompleteScreenDetail(), response.getCompleteScreenDetail());
            assertEquals(Boolean.TRUE, response.isAutoSaveSlip());
            verify(creditCardService, times(1)).deleteCreditCardCache(anyString(), anyString());
            verify(customerExpService, times(1)).deleteCreditCardCache(anyString(), anyString(), anyString(), anyString());
            verify(baseConfirmServiceHelper, times(1)).baseExecuteCallbackIfConfiguredAsync(eq(draftCache), anyString(), anyString());
            verify(notificationCommonPaymentService, times(1)).sendENotification(any());
        }

        @Test
        void testConfirmCreditCardPaymentWithException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            Class<TMBCommonException> throwExceptionClass = TMBCommonException.class;
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            doThrow(throwExceptionClass).when(paymentService).confirmCreditCardPayment(anyString(), anyString(), anyString(), any(CreditCardConfirmRequest.class));
            doThrow(throwExceptionClass).when(baseConfirmServiceHelper).baseHandleException(any(), any(throwExceptionClass));

            assertThrows(TMBCommonException.class, () -> creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));
        }

        @Test
        void testConfirmWhenSuccessWhenPayByOwnerThenReturnConfirmationResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
            boolean isPayByOwner = true;
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(isPayByOwner);
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            when(paymentService.confirmCreditCardPayment(anyString(), anyString(), anyString(), any(CreditCardConfirmRequest.class)))
                    .thenReturn(creditCardConfirmResponse);

            ConfirmationCommonPaymentResponse response = creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache);

            assertNotNull(response);
            assertEquals(new BigDecimal(creditCardConfirmResponse.getBillPayment().getPayerAccount().getBalances().getAvailable()).setScale(2, RoundingMode.HALF_DOWN),
                    response.getRemainingBalance());
            assertEquals(Boolean.TRUE, response.isAutoSaveSlip());
            assertNotNull(response.getReferenceNo());
            verify(baseConfirmServiceHelper, never()).baseUpdatePinFreeCountWithCondition(anyString(), anyString(), any(), anyBoolean());
            verify(dailyLimitService, never()).updateAccumulateUsage(any(), any(), anyString(), anyString());
            verify(baseConfirmServiceHelper).baseClearDraftDataCache(anyString());
            verify(customersTransactionService).clearDepositCache(anyString(), anyString());
            verify(creditCardService, times(1)).deleteCreditCardCache(anyString(), anyString());
            verify(customerExpService, times(1)).deleteCreditCardCache(anyString(), anyString(), anyString(), anyString());
            verify(baseConfirmServiceHelper, times(1)).baseExecuteCallbackIfConfiguredAsync(eq(draftCache), anyString(), anyString());
            verify(notificationCommonPaymentService, times(1)).sendENotification(any());

        }

        @Test
        void testUpdateAccumulateUsageWhenNotOwnerThenCallDailyLimitService() throws TMBCommonException, TMBCommonExceptionWithResponse {
            boolean isPayByOwner = false;
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(isPayByOwner);
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            when(paymentService.confirmCreditCardPayment(anyString(), anyString(), anyString(), any(CreditCardConfirmRequest.class)))
                    .thenReturn(creditCardConfirmResponse);

            assertDoesNotThrow(() -> creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            verify(baseConfirmServiceHelper, times(1)).baseUpdatePinFreeCountWithCondition(anyString(), anyString(), any(), anyBoolean());
            verify(dailyLimitService, times(1)).updateAccumulateUsage(any(), any(), anyString(), anyString());
            verify(baseConfirmServiceHelper).baseClearDraftDataCache(anyString());
            verify(customersTransactionService).clearDepositCache(anyString(), anyString());
            verify(creditCardService, times(1)).deleteCreditCardCache(anyString(), anyString());
            verify(customerExpService, times(1)).deleteCreditCardCache(anyString(), anyString(), anyString(), anyString());
            verify(baseConfirmServiceHelper, times(1)).baseExecuteCallbackIfConfiguredAsync(eq(draftCache), anyString(), anyString());
            verify(notificationCommonPaymentService, times(1)).sendENotification(any());
        }

        @Test
        void testConfirmWhenPaymentServiceThrowsExceptionThenThrowException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            TMBCommonException throwException = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            when(paymentService.confirmCreditCardPayment(anyString(), anyString(), anyString(), any(CreditCardConfirmRequest.class)))
                    .thenThrow(throwException);
            when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenReturn(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
        }
    }

    @Nested
    class validateDateTests {
        @Test
        void testValidatePaymentConfirmWhenFailedValidateServiceHoursShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.UNAUTHORIZED, null);
            doThrow(throwException).when(commonValidateConfirmationService).validateServiceHours(eq(draftCache));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
            verify(commonValidateConfirmationService, never()).verifyAuthentication(anyString(), eq(draftCache), eq(headers));
            verify(commonValidateConfirmationService, never()).validateTransactionByTransactionId(anyString());
            verify(dailyLimitService, never()).validateDailyLimitExceeded(anyString(), any(), any());
        }

        @Test
        void testValidatePaymentConfirmWhenFailedVerifyAuthenticationShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.UNAUTHORIZED, null);
            doThrow(throwException).when(commonValidateConfirmationService).verifyAuthentication(anyString(), eq(draftCache), eq(headers));
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
            verify(commonValidateConfirmationService).validateServiceHours(eq(draftCache));
            verify(commonValidateConfirmationService, never()).validateTransactionByTransactionId(anyString());
            verify(dailyLimitService, never()).validateDailyLimitExceeded(anyString(), any(), any());
        }

        @Test
        void testValidatePaymentConfirmWhenFailedValidateTransactionByTransactionIdShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.UNAUTHORIZED, null);
            doThrow(throwException).when(commonValidateConfirmationService).validateTransactionByTransactionId(anyString());
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
            verify(commonValidateConfirmationService).validateServiceHours(eq(draftCache));
            verify(commonValidateConfirmationService).verifyAuthentication(anyString(), eq(draftCache), eq(headers));
            verify(dailyLimitService, never()).validateDailyLimitExceeded(anyString(), any(), any());
        }

        @Test
        void testValidatePaymentConfirmWhenFailedValidateDailyLimitShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            draftCache.getValidateDraftCache().setAdditionalParam(new AdditionalParamCommonPaymentDraftCache().setPayByOwner(false));
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            TMBCommonException throwException = new TMBCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), ResponseCode.AUTHORIZE_FAILED.getMessage(), ResponseCode.AUTHORIZE_FAILED.getService(), HttpStatus.UNAUTHORIZED, null);
            doThrow(throwException).when(dailyLimitService).validateDailyLimitExceeded(anyString(), any(), any());
            when(baseConfirmServiceHelper.baseHandleException(any(), any(throwException.getClass()))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));

            assertEquals(ResponseCode.AUTHORIZE_FAILED.getCode(), exception.getErrorCode());
            verify(commonValidateConfirmationService).validateServiceHours(eq(draftCache));
            verify(commonValidateConfirmationService).verifyAuthentication(anyString(), eq(draftCache), eq(headers));
            verify(commonValidateConfirmationService).validateTransactionByTransactionId(anyString());
        }

    }

    @Nested
    class ProcessAfterConfirmWithExternal {
        @Test
        void testProcessAfterConfirmWithExternal_WhenFailedExceptionAfterCallExternalServiceSuccess_ShouldDoesNotThrow() throws TMBCommonException {
            draftCache.getValidateDraftCache().getAdditionalParam().setPayByOwner(true);
            mockGetBasePrepareDataConfirmReturnData(customerProfile);
            createCreditCardConfirmResponse();
            doThrow(NullPointerException.class).when(creditCardPaymentConfirmServiceProcessor).processAfterConfirmWithExternal(any(), any(), any(), any(), any());

            assertDoesNotThrow(() -> creditCardPaymentConfirmServiceProcessor.executeConfirm(request, headers, draftCache));
        }

    }

    @Nested
    class SaveSuccessLogTest {
        @Test
        void testSaveSuccessLog_ShouldUpdateFinLogTxnBal() {
            String expectAvailableBalance = "1050.50";
            BaseConfirmLogRecord baseConfirmLogRecord = new BaseConfirmLogRecord("referenceId", crmId, correlationId, "transactionTime", ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID);
            var financialLog = FinancialLogMapper.INSTANCE.mapToFinancialCreditCardBillPayActivityLog(baseConfirmLogRecord, "activityLogTypeId", draftCache);
            CreditCardConfirmResponse externalResponse = new CreditCardConfirmResponse();
            externalResponse.setBillPayment(new BillPaymentCreditCard());
            externalResponse.getBillPayment().setPayerAccount(new PayerAccount());
            externalResponse.getBillPayment().getPayerAccount().setBalances(new AccountBalance());
            externalResponse.getBillPayment().getPayerAccount().getBalances().setAvailable(expectAvailableBalance);
            CreditCardLogsConfirm logEvents = new CreditCardLogsConfirm()
                    .setFinancialCreditCardBillPayActivityLog(financialLog)
                    .setActivityCreditCardBillPayConfirmationEvent(null)
                    .setTransactionActivityCreditCardBillPay(null);

            creditCardPaymentConfirmServiceProcessor.saveSuccessLog(request, headers, draftCache, null, externalResponse, logEvents, null);

            ArgumentCaptor<FinancialRequest> captor = ArgumentCaptor.forClass(FinancialRequest.class);
            verify(logEventPublisherService, times(1)).saveFinancialLog(anyString(), captor.capture());
            assertEquals(expectAvailableBalance, captor.getValue().getTxnBal());
        }
    }

    @Nested
    class SaveFailedLogTest {

        @Test
        void testSaveFailedLog_WhenTmbCommonException_ShouldUpdateCorrectFieldAndPublishLog() {
            BaseConfirmLogRecord baseConfirmLogRecord = new BaseConfirmLogRecord("referenceId", crmId, correlationId, "transactionTime", ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID);
            var activityLog = ActivityLogConfirmMapper.INSTANCE.mapToActivityCreditCardBillPayConfirmationEvent(new HttpHeaders(), draftCache);
            var financialLog = FinancialLogMapper.INSTANCE.mapToFinancialCreditCardBillPayActivityLog(baseConfirmLogRecord, activityLog.getActivityTypeId(), draftCache);
            var transactionLog = TransactionLogMapper.INSTANCE.mapToTransactionActivityCreditCardBillPay(baseConfirmLogRecord, draftCache);
            CreditCardLogsConfirm logEvents = new CreditCardLogsConfirm()
                    .setFinancialCreditCardBillPayActivityLog(financialLog)
                    .setActivityCreditCardBillPayConfirmationEvent(activityLog)
                    .setTransactionActivityCreditCardBillPay(transactionLog);
            Exception exception = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);

            creditCardPaymentConfirmServiceProcessor.saveFailedLog(request, headers, draftCache, null, logEvents, exception);

            ArgumentCaptor<ActivityCreditCardBillPayConfirmationEvent> captureActivityLog = ArgumentCaptor.forClass(ActivityCreditCardBillPayConfirmationEvent.class);
            ArgumentCaptor<FinancialCreditCardBillPayActivityLog> captureFinancialLog = ArgumentCaptor.forClass(FinancialCreditCardBillPayActivityLog.class);
            ArgumentCaptor<TransactionActivityCreditCardBillPay> captureTransactionLog = ArgumentCaptor.forClass(TransactionActivityCreditCardBillPay.class);
            verify(logEventPublisherService, times(1)).saveActivityLog(captureActivityLog.capture());
            verify(logEventPublisherService, times(1)).saveFinancialLog(eq(correlationId), captureFinancialLog.capture());
            verify(logEventPublisherService, times(1)).saveTransactionLog(eq(correlationId), captureTransactionLog.capture());
            assertEquals(ACTIVITY_FAILURE, captureActivityLog.getValue().getActivityStatus());
            assertNotNull(captureActivityLog.getValue().getFailReason());
            assertEquals(ACTIVITY_FAILURE, captureFinancialLog.getValue().getTxnStatus());
            assertNotNull(captureFinancialLog.getValue().getErrorCd());
            assertEquals(ACTIVITY_FAILURE, captureTransactionLog.getValue().getTransactionStatus());
        }
    }

    @Nested
    class HandleExceptionTest {
        @Test
        void testHandleException_WhenTMBCommonException_ShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
            TMBCommonException throwException = new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
            when(baseConfirmServiceHelper.baseHandleException(any(), any())).thenReturn(new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null));

            TMBCommonException exception = creditCardPaymentConfirmServiceProcessor.handleException(request, headers, draftCache, null, null, throwException);

            assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
        }
    }

    private CommonPaymentDraftCache createDraftCache() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();

        PaymentInformation paymentInformation = new PaymentInformation();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductRef1("****************");
        productDetail.setProductRef2("Ref2");
        productDetail.setProductRef3("Ref3");
        paymentInformation.setProductDetail(productDetail);
        paymentInformation.setCompCode("comp-test-001");

        AmountDetail amountDetail = new AmountDetail();
        amountDetail.setAmountValue(new BigDecimal("1000.00"));
        amountDetail.setAmountLabelEn("Amount Label En");
        amountDetail.setAmountLabelTh("Amount Label Th");
        paymentInformation.setAmountDetail(amountDetail);

        CreditCardConfirmRequest creditCardConfirmRequest = new CreditCardConfirmRequest();
        creditCardConfirmRequest.setBillPayment(new BillPaymentCreditCard()
                .setAmount("1000.00")
                .setEpayCode("epay-test-001")
                .setCompCode("comp-test-001")
                .setPaymentId("2025072315265100031")
                .setEpayCode("202507231500000054")
                .setRef1("430840XXXXXX3577")
                .setPayerAccount(new PayerAccount()
                        .setId("**********")
                        .setType("SDA"))
                .setPayeeCard(new PayeeCard()
                        .setAccountId("0000000060010350399003577")
                        .setCardId("430840XXXXXX3577")
                        .setCardEmbossingName("OneApp25537153")
                        .setExpiryDate("4006")
                        .setProductGroupId("VABSSN")
                        .setProductId("1"))
                .setBankId("2025072315265100031")
                .setFee(new CreditCardFee(new BigDecimal("0.00"))));

        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        validateRequest.setCreditCard(new CreditCardValidationCommonPaymentRequest());
        validateRequest.getCreditCard().setAccountId("4111********1111");
        validateRequest.setDeposit(new DepositValidationCommonPaymentRequest());
        validateRequest.getDeposit().setAccountNumber("**********");

        AdditionalParamCommonPaymentDraftCache additionalParam = new AdditionalParamCommonPaymentDraftCache();
        additionalParam.setPayByOwner(false);
        validateDraftCache.setAdditionalParam(additionalParam);

        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setBillerGroupType("0");
        billerInfo.setBillerCompCode("CREDIT_CARD");
        billerInfo.setNameEn("Credit Card Biller");
        billerInfo.setPaymentMethod("15");
        billerInfo.setBillerMethod("3");
        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(new ReferenceResponse());
        validateDraftCache.setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));

        validateDraftCache.setFromDepositAccount(new DepositAccountInCache());
        validateDraftCache.getFromDepositAccount().setAccountNumber("**********");

        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setCreditCardConfirmRequest(creditCardConfirmRequest));

        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();
        commonPaymentConfig.setCallbackUrls(Collections.singletonList(CALLBACK_URL));

        cache.setCommonPaymentConfig(commonPaymentConfig);
        cache.setValidateDraftCache(validateDraftCache);
        cache.setValidateRequest(validateRequest);
        cache.setPaymentInformation(paymentInformation);

        return cache;
    }

    private CreditCardConfirmResponse createCreditCardConfirmResponse() {
        CreditCardConfirmResponse creditCardConfirmResponse = new CreditCardConfirmResponse();
        BillPaymentCreditCard billPayment = new BillPaymentCreditCard();

        PayerAccount payerAccount = new PayerAccount();
        payerAccount.setId("**********");
        payerAccount.setType("SDA");
        payerAccount.setTitle("Savings Account");

        AccountBalance accountBalance = new AccountBalance();
        accountBalance.setAvailable("5000.00");
        payerAccount.setBalances(accountBalance);

        PayeeCard payeeCard = new PayeeCard();
        payeeCard.setAccountId("****************");
        payeeCard.setCardId("****************");
        payeeCard.setCardEmbossingName("Test Card");
        payeeCard.setExpiryDate("12/25");
        payeeCard.setProductGroupId("MC");
        payeeCard.setProductId("VIS");

        billPayment.setPayerAccount(payerAccount);
        billPayment.setPayeeCard(payeeCard);
        billPayment.setAmount("1000.00");
        billPayment.setCompCode("comp-test-001");
        billPayment.setEpayCode("epay-test-001");
        billPayment.setPaymentId("payment-001");
        billPayment.setBankRefId("bank-ref-001");
        billPayment.setRef1("ref1-001");
        billPayment.setRef2("ref2-001");
        billPayment.setRequestUid("req-uid-001");
        billPayment.setRequestDateTime("2023-01-01T12:00:00Z");
        billPayment.setBankId("004");
        billPayment.setBranchId("0001");
        billPayment.setChannelId("MB");
        billPayment.setTellerId("auto");
        billPayment.setCurrency("THB");

        CreditCardFee fee = new CreditCardFee(new BigDecimal("0.00"));
        billPayment.setFee(fee);

        creditCardConfirmResponse.setBillPayment(billPayment);
        return creditCardConfirmResponse;
    }

    private void setupPaymentServiceMocks() throws TMBCommonException, TMBCommonExceptionWithResponse {
        when(paymentService.confirmCreditCardPayment(anyString(), anyString(), anyString(), any(CreditCardConfirmRequest.class)))
                .thenReturn(creditCardConfirmResponse);
    }

    private void mockGetBasePrepareDataConfirmReturnData(CustomerCrmProfile customerProfile) throws TMBCommonException {
        basePrepareDataConfirm.setCustomerCrmProfile(customerProfile);
        basePrepareDataConfirm.setTransactionTime(String.valueOf(System.currentTimeMillis()));
        when(baseConfirmServiceHelper.getBasePrepareDataConfirm(any(), anyString())).thenReturn(basePrepareDataConfirm);
    }
}