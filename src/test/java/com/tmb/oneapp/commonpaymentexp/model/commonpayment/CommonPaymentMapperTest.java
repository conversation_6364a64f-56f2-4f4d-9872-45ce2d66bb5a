package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class CommonPaymentMapperTest {

    @Test
    void testToCommonPaymentDraftCache_WhenSet2DecimalRoundingDown_ShouldReturnCorrectly() {
        String inputMoreThan2Decimal = "10.51987654";
        PaymentInformation paymentInformation = new PaymentInformation();
        paymentInformation.setAmountDetail(new AmountDetail().setAmountValue(new BigDecimal(inputMoreThan2Decimal)));

        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setAmount("1000.00");
        deepLinkRequest.setCallFrom("autoLoan");

        InitialPrepareData prepareData = new InitialPrepareData()
                .setCommonPaymentConfig(new CommonPaymentConfig())
                .setDeepLinkRequest(deepLinkRequest);

        CommonPaymentDraftCache actual = CommonPaymentMapper.INSTANCE.toCommonPaymentDraftCache(paymentInformation, "processor-key", prepareData, "test-partner");

        String expected2Decimal = "10.51";
        assertEquals(new BigDecimal(expected2Decimal), actual.getPaymentInformation().getAmountDetail().getAmountValue());
        assertEquals("processor-key", actual.getProcessorType());
        assertEquals("test-partner", actual.getPartnerName());
        assertNotNull(actual.getCommonPaymentConfig());
        assertNotNull(actual.getDeepLinkRequest());
    }
}