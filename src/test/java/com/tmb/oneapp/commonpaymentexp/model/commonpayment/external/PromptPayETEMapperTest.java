package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external;

import com.tmb.common.cache.Transaction;
import com.tmb.common.cache.service.TransactionServices;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.EDonationValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay.PromptPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay.PromptPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CATEGORY_E_DONATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.SENDER_TYPE_KEY_IN;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TERMINAL_TYPE;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TMB_BANK_SHORT_NAME;
import static com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.BillPromptPayValidationProcessor.THAI_NATION_ID_TYPE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

@ExtendWith(MockitoExtension.class)
class PromptPayETEMapperTest {
    @Mock
    TransactionServices transactionServices;

    private DepositAccount fromDepositAccount;
    private ValidationCommonPaymentRequest request;
    private PaymentInformation paymentInformation;
    private MasterBillerResponse masterBiller;
    private CustomerKYCResponse customerKYC;

    @BeforeEach
    void setUp() {
        Transaction.setTransactionServices(transactionServices);

        String compCode = "*************45";
        fromDepositAccount = new DepositAccount()
                .setAccountNumber("**********")
                .setAccountType("SDA")
                .setAccountName("account-name");
        paymentInformation = new PaymentInformation()
                .setCompCode(compCode)
                .setProductDetail(new ProductDetail()
                        .setProductRef1("reference1")
                        .setProductRef2("reference2")
                        .setProductRef3("reference3"));
        masterBiller = new MasterBillerResponse().setBillerInfo(new BillerInfoResponse()
                .setBillerCategoryCode("biller-category-code")
        );
        customerKYC = new CustomerKYCResponse().setIdNo("*************");
        request = new ValidationCommonPaymentRequest().setDeposit(new DepositValidationCommonPaymentRequest().setAmount(BigDecimal.valueOf(9500.50)));

    }

    @Nested
    class EDonationTransactionTest {
        @Test
        void testToPromptPayETERequestForPromptPayPayment_WhenIsEDonationTransactionAndThaiNationAndAllowShareToRd_ShouldReturnCorrectPromptPayETERequestValidate() {
            masterBiller.getBillerInfo().setBillerCategoryCode(CATEGORY_E_DONATION_ID);
            customerKYC.setIdType(THAI_NATION_ID_TYPE);
            request.setEDonation(new EDonationValidationCommonPaymentRequest().setAllowShareToRdFlag(true));
            CommonPaymentDraftCache cache = new CommonPaymentDraftCache()
                    .setPaymentInformation(paymentInformation);
            PromptPayPrepareDataValidate prepareData = new PromptPayPrepareDataValidate()
                    .setMasterBillerResponse(masterBiller)
                    .setFromDepositAccount(fromDepositAccount)
                    .setCustomerKYC(customerKYC);
            boolean isAmountExceedAmloThreshold = true;
            boolean isShouldCallToETEISO20022 = true;

            PromptPayETEValidateRequest actual = PromptPayETEMapper.INSTANCE.toPromptPayETERequestForValidatePromptPayPayment(request, cache, prepareData, isAmountExceedAmloThreshold, isShouldCallToETEISO20022);

            String expectedRef2 = customerKYC.getIdNo();
            // Sender
            assertEquals(fromDepositAccount.getAccountNumber(), actual.getSender().getAccountId());
            assertEquals(fromDepositAccount.getAccountType(), actual.getSender().getAccountType());
            assertEquals(TMB_BANK_SHORT_NAME, actual.getSender().getBankName());
            assertEquals(fromDepositAccount.getAccountName(), actual.getSender().getAccountName());
            assertEquals(customerKYC.getIdNo(), actual.getSender().getTaxId());
            assertEquals(SENDER_TYPE_KEY_IN, actual.getSender().getCustomerTypeFlag());

            // Receiver
            assertEquals(cache.getPaymentInformation().getCompCode(), actual.getReceiver().getId());

            // Terminal
            assertNotNull(actual.getTerminal().getId());
            assertEquals(TERMINAL_TYPE, actual.getTerminal().getType());

            assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef1().toUpperCase(), actual.getReference1());
            assertEquals(expectedRef2, actual.getReference2());
            assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef3(), actual.getReference3());
            assertEquals(request.getDeposit().getAmount(), actual.getAmount());
            assertNotNull(actual.getTransactionCreatedDatetime());
            assertNotNull(actual.getInstructionId());
        }

        @Test
        void testToPromptPayETERequestForPromptPayPayment_WhenIsEDonationTransactionButNotThaiNationAndAllowShareToRd_ShouldReturnCorrectPromptPayETERequestValidate() {
            masterBiller.getBillerInfo().setBillerCategoryCode(CATEGORY_E_DONATION_ID);
            customerKYC.setIdType("not-thai-nation");
            request.setEDonation(new EDonationValidationCommonPaymentRequest().setAllowShareToRdFlag(true));
            CommonPaymentDraftCache cache = new CommonPaymentDraftCache()
                    .setPaymentInformation(paymentInformation);
            PromptPayPrepareDataValidate prepareData = new PromptPayPrepareDataValidate()
                    .setMasterBillerResponse(masterBiller)
                    .setFromDepositAccount(fromDepositAccount)
                    .setCustomerKYC(customerKYC);
            boolean isAmountExceedAmloThreshold = true;
            boolean isShouldCallToETEISO20022 = true;

            PromptPayETEValidateRequest actual = PromptPayETEMapper.INSTANCE.toPromptPayETERequestForValidatePromptPayPayment(request, cache, prepareData, isAmountExceedAmloThreshold, isShouldCallToETEISO20022);

            String expectedRef2 = cache.getPaymentInformation().getProductDetail().getProductRef2();
            // Sender
            assertEquals(fromDepositAccount.getAccountNumber(), actual.getSender().getAccountId());
            assertEquals(fromDepositAccount.getAccountType(), actual.getSender().getAccountType());
            assertEquals(TMB_BANK_SHORT_NAME, actual.getSender().getBankName());
            assertEquals(fromDepositAccount.getAccountName(), actual.getSender().getAccountName());
            assertEquals(customerKYC.getIdNo(), actual.getSender().getTaxId());
            assertEquals(SENDER_TYPE_KEY_IN, actual.getSender().getCustomerTypeFlag());

            // Receiver
            assertEquals(cache.getPaymentInformation().getCompCode(), actual.getReceiver().getId());

            // Terminal
            assertNotNull(actual.getTerminal().getId());
            assertEquals(TERMINAL_TYPE, actual.getTerminal().getType());

            assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef1().toUpperCase(), actual.getReference1());
            assertEquals(expectedRef2, actual.getReference2());
            assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef3(), actual.getReference3());
            assertEquals(request.getDeposit().getAmount(), actual.getAmount());
            assertNotNull(actual.getTransactionCreatedDatetime());
            assertNotNull(actual.getInstructionId());
        }

        @Test
        void testToPromptPayETERequestForPromptPayPayment_WhenIsEDonationTransactionAndThaiNationButNotAllowShareToRd_ShouldReturnCorrectPromptPayETERequestValidate() {
            boolean isNotAllowShareToRdFlag = false;
            masterBiller.getBillerInfo().setBillerCategoryCode(CATEGORY_E_DONATION_ID);
            customerKYC.setIdType(THAI_NATION_ID_TYPE);
            request.setEDonation(new EDonationValidationCommonPaymentRequest().setAllowShareToRdFlag(isNotAllowShareToRdFlag));
            CommonPaymentDraftCache cache = new CommonPaymentDraftCache()
                    .setPaymentInformation(paymentInformation);
            PromptPayPrepareDataValidate prepareData = new PromptPayPrepareDataValidate()
                    .setMasterBillerResponse(masterBiller)
                    .setFromDepositAccount(fromDepositAccount)
                    .setCustomerKYC(customerKYC);
            boolean isAmountExceedAmloThreshold = true;
            boolean isShouldCallToETEISO20022 = true;

            PromptPayETEValidateRequest actual = PromptPayETEMapper.INSTANCE.toPromptPayETERequestForValidatePromptPayPayment(request, cache, prepareData, isAmountExceedAmloThreshold, isShouldCallToETEISO20022);

            String expectedRef2 = cache.getPaymentInformation().getProductDetail().getProductRef2();
            // Sender
            assertEquals(fromDepositAccount.getAccountNumber(), actual.getSender().getAccountId());
            assertEquals(fromDepositAccount.getAccountType(), actual.getSender().getAccountType());
            assertEquals(TMB_BANK_SHORT_NAME, actual.getSender().getBankName());
            assertEquals(fromDepositAccount.getAccountName(), actual.getSender().getAccountName());
            assertEquals(customerKYC.getIdNo(), actual.getSender().getTaxId());
            assertEquals(SENDER_TYPE_KEY_IN, actual.getSender().getCustomerTypeFlag());

            // Receiver
            assertEquals(cache.getPaymentInformation().getCompCode(), actual.getReceiver().getId());

            // Terminal
            assertNotNull(actual.getTerminal().getId());
            assertEquals(TERMINAL_TYPE, actual.getTerminal().getType());

            assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef1().toUpperCase(), actual.getReference1());
            assertEquals(expectedRef2, actual.getReference2());
            assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef3(), actual.getReference3());
            assertEquals(request.getDeposit().getAmount(), actual.getAmount());
            assertNotNull(actual.getTransactionCreatedDatetime());
            assertNotNull(actual.getInstructionId());
        }

        @Test
        void testToPromptPayETERequestForPromptPayPayment_WhenIsEDonationTransactionButNotThaiNationButNotAllowShareToRd_ShouldReturnCorrectPromptPayETERequestValidate() {
            boolean isNotAllowShareToRdFlag = false;
            masterBiller.getBillerInfo().setBillerCategoryCode(CATEGORY_E_DONATION_ID);
            customerKYC.setIdType("not-thai-nation");
            request.setEDonation(new EDonationValidationCommonPaymentRequest().setAllowShareToRdFlag(isNotAllowShareToRdFlag));
            CommonPaymentDraftCache cache = new CommonPaymentDraftCache()
                    .setPaymentInformation(paymentInformation);
            PromptPayPrepareDataValidate prepareData = new PromptPayPrepareDataValidate()
                    .setMasterBillerResponse(masterBiller)
                    .setFromDepositAccount(fromDepositAccount)
                    .setCustomerKYC(customerKYC);
            boolean isAmountExceedAmloThreshold = true;
            boolean isShouldCallToETEISO20022 = true;

            PromptPayETEValidateRequest actual = PromptPayETEMapper.INSTANCE.toPromptPayETERequestForValidatePromptPayPayment(request, cache, prepareData, isAmountExceedAmloThreshold, isShouldCallToETEISO20022);

            String expectedRef2 = cache.getPaymentInformation().getProductDetail().getProductRef2();
            // Sender
            assertEquals(fromDepositAccount.getAccountNumber(), actual.getSender().getAccountId());
            assertEquals(fromDepositAccount.getAccountType(), actual.getSender().getAccountType());
            assertEquals(TMB_BANK_SHORT_NAME, actual.getSender().getBankName());
            assertEquals(fromDepositAccount.getAccountName(), actual.getSender().getAccountName());
            assertEquals(customerKYC.getIdNo(), actual.getSender().getTaxId());
            assertEquals(SENDER_TYPE_KEY_IN, actual.getSender().getCustomerTypeFlag());

            // Receiver
            assertEquals(cache.getPaymentInformation().getCompCode(), actual.getReceiver().getId());

            // Terminal
            assertNotNull(actual.getTerminal().getId());
            assertEquals(TERMINAL_TYPE, actual.getTerminal().getType());

            assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef1().toUpperCase(), actual.getReference1());
            assertEquals(expectedRef2, actual.getReference2());
            assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef3(), actual.getReference3());
            assertEquals(request.getDeposit().getAmount(), actual.getAmount());
            assertNotNull(actual.getTransactionCreatedDatetime());
            assertNotNull(actual.getInstructionId());
        }
    }

    @Test
    void testToPromptPayETERequestForPromptPayPayment_WhenAmountExceedAmloThreshold_ShouldReturnCorrectPromptPayETERequestValidate() {
        boolean isAmountExceedAmloThreshold = true;
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache()
                .setPaymentInformation(paymentInformation);
        PromptPayPrepareDataValidate prepareData = new PromptPayPrepareDataValidate()
                .setMasterBillerResponse(masterBiller)
                .setFromDepositAccount(fromDepositAccount)
                .setCustomerKYC(customerKYC);
        boolean isShouldCallToETEISO20022 = true;

        PromptPayETEValidateRequest actual = PromptPayETEMapper.INSTANCE.toPromptPayETERequestForValidatePromptPayPayment(request, cache, prepareData, isAmountExceedAmloThreshold, isShouldCallToETEISO20022);

        String expectedTaxId = customerKYC.getIdNo();
        // Sender
        assertEquals(fromDepositAccount.getAccountNumber(), actual.getSender().getAccountId());
        assertEquals(fromDepositAccount.getAccountType(), actual.getSender().getAccountType());
        assertEquals(TMB_BANK_SHORT_NAME, actual.getSender().getBankName());
        assertEquals(fromDepositAccount.getAccountName(), actual.getSender().getAccountName());
        assertEquals(expectedTaxId, actual.getSender().getTaxId());
        assertEquals(SENDER_TYPE_KEY_IN, actual.getSender().getCustomerTypeFlag());

        // Receiver
        assertEquals(cache.getPaymentInformation().getCompCode(), actual.getReceiver().getId());

        // Terminal
        assertNotNull(actual.getTerminal().getId());
        assertEquals(TERMINAL_TYPE, actual.getTerminal().getType());

        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef1().toUpperCase(), actual.getReference1());
        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef2(), actual.getReference2());
        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef3(), actual.getReference3());
        assertEquals(request.getDeposit().getAmount(), actual.getAmount());
        assertNotNull(actual.getTransactionCreatedDatetime());
        assertNotNull(actual.getInstructionId());
    }

    @Test
    void testToPromptPayETERequestForPromptPayPayment_WhenisShouldCallToETEISO20022_ShouldReturnCorrectPromptPayETERequestValidate() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache()
                .setPaymentInformation(paymentInformation);
        PromptPayPrepareDataValidate prepareData = new PromptPayPrepareDataValidate()
                .setMasterBillerResponse(masterBiller)
                .setFromDepositAccount(fromDepositAccount)
                .setCustomerKYC(customerKYC);
        boolean isAmountExceedAmloThreshold = false;
        boolean isShouldCallToETEISO20022 = true;

        PromptPayETEValidateRequest actual = PromptPayETEMapper.INSTANCE.toPromptPayETERequestForValidatePromptPayPayment(request, cache, prepareData, isAmountExceedAmloThreshold, isShouldCallToETEISO20022);

        // Sender
        assertEquals(fromDepositAccount.getAccountNumber(), actual.getSender().getAccountId());
        assertEquals(fromDepositAccount.getAccountType(), actual.getSender().getAccountType());
        assertEquals(TMB_BANK_SHORT_NAME, actual.getSender().getBankName());
        assertEquals(fromDepositAccount.getAccountName(), actual.getSender().getAccountName());
        assertNull(actual.getSender().getTaxId());
        assertEquals(SENDER_TYPE_KEY_IN, actual.getSender().getCustomerTypeFlag());

        // Receiver
        assertEquals(cache.getPaymentInformation().getCompCode(), actual.getReceiver().getId());

        // Terminal
        assertNotNull(actual.getTerminal().getId());
        assertEquals(TERMINAL_TYPE, actual.getTerminal().getType());

        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef1().toUpperCase(), actual.getReference1());
        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef2(), actual.getReference2());
        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef3(), actual.getReference3());
        assertEquals(request.getDeposit().getAmount(), actual.getAmount());
        assertNotNull(actual.getTransactionCreatedDatetime());
        assertNotNull(actual.getInstructionId());
    }

    @Test
    void testToPromptPayETERequestForPromptPayPayment_WhenNormalBillPromptPay_ShouldReturnCorrectPromptPayETERequestValidate() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache()
                .setPaymentInformation(paymentInformation);
        PromptPayPrepareDataValidate prepareData = new PromptPayPrepareDataValidate()
                .setMasterBillerResponse(masterBiller)
                .setFromDepositAccount(fromDepositAccount)
                .setCustomerKYC(customerKYC);
        boolean isAmountExceedAmloThreshold = false;
        boolean isShouldCallToETEISO20022 = false;

        PromptPayETEValidateRequest actual = PromptPayETEMapper.INSTANCE.toPromptPayETERequestForValidatePromptPayPayment(request, cache, prepareData, isAmountExceedAmloThreshold, isShouldCallToETEISO20022);

        // Sender
        assertEquals(fromDepositAccount.getAccountNumber(), actual.getSender().getAccountId());
        assertEquals(fromDepositAccount.getAccountType(), actual.getSender().getAccountType());
        assertEquals(TMB_BANK_SHORT_NAME, actual.getSender().getBankName());
        assertEquals(fromDepositAccount.getAccountName(), actual.getSender().getAccountName());
        assertNull(actual.getSender().getTaxId());
        assertEquals(SENDER_TYPE_KEY_IN, actual.getSender().getCustomerTypeFlag());

        // Receiver
        assertEquals(cache.getPaymentInformation().getCompCode(), actual.getReceiver().getId());

        // Terminal
        assertNotNull(actual.getTerminal().getId());
        assertEquals(TERMINAL_TYPE, actual.getTerminal().getType());

        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef1().toUpperCase(), actual.getReference1());
        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef2(), actual.getReference2());
        assertEquals(cache.getPaymentInformation().getProductDetail().getProductRef3(), actual.getReference3());
        assertEquals(request.getDeposit().getAmount(), actual.getAmount());
        assertNotNull(actual.getTransactionCreatedDatetime());
        assertNull(actual.getInstructionId());
    }

    @Test
    void testToPromptPayETERequestForConfirmPromptPayPayment_ShouldReturnCorrectPromptPayETERequest() {
        mockGetTransactionIdReturnData();
        PromptPayExternalValidateResponse externalValidateResponse = new PromptPayExternalValidateResponse();
        var eteRequest = new PromptPayETEValidateRequest()
                .setReference1("reference1-from-request")
                .setReference2("reference2-from-request")
                .setReference3("reference3-from-request")
                .setInstructionId("instruction-id-from-request");
        var eteResponse = new PromptPayETEValidateResponse()
                .setReference1("reference1-from-response")
                .setAmount(BigDecimal.valueOf(9500.50));

        externalValidateResponse.setEteRequest(eteRequest);
        externalValidateResponse.setEteResponse(eteResponse);

        PromptPayETEConfirmRequest actual = PromptPayETEMapper.INSTANCE.toPromptPayETERequestForConfirmPromptPayPayment(externalValidateResponse);

        // Same as ETE response
        assertEquals(eteResponse.getReference1(), actual.getReference1());
        assertEquals(eteResponse.getAmount(), actual.getAmount());

        // Same as ETE request
        assertEquals(eteRequest.getReference2(), actual.getReference2());
        assertEquals(eteRequest.getReference3(), actual.getReference3());
        assertEquals(eteRequest.getInstructionId(), actual.getInstructionId());
        assertEquals("transaction-id-after-running-sequence", actual.getTransactionReference());

    }

    private void mockGetTransactionIdReturnData() {
        Mockito.when(Transaction.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, 8)).thenReturn("transaction-id-after-running-sequence");
    }
}