package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableRunnable;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableSupplier;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;

/**
 * Utility class for testing private methods via reflection
 */
public class TestUtils {

    /**
     * Invoke a private method on an object using reflection
     *
     * @param service    The object to invoke the method on
     * @param methodName The name of the private method
     * @param args       The arguments to pass to the method
     * @return The return value of the method
     */
    public static <T> T invokeMethod(Object service, String methodName, Object... args) {
        return ReflectionTestUtils.invokeMethod(service, methodName, args);
    }

    /**
     * Set a field value on an object using reflection
     *
     * @param service   The object to set the field on
     * @param fieldName The name of the field
     * @param value     The value to set
     */
    public static void setField(Object service, String fieldName, Object value) {
        ReflectionTestUtils.setField(service, fieldName, value);
    }

    /**
     * Invoke a private method on an object using reflection
     *
     * @param service    The object to invoke the method on
     * @param methodName The name of the private method
     * @param args       The arguments to pass to the method
     * @return The return value of the method
     * @throws Exception If any error occurs during reflection
     */
    public static <T> T invokeMethodWithThrow(Object service, String methodName, Object... args) throws Throwable {
        try {
            return ReflectionTestUtils.invokeMethod(service, methodName, args);
        } catch (UndeclaredThrowableException ex) {
            if (ex.getCause() != null) {
                throw ex.getCause();
            }

            throw ex;
        }
    }

    public static void setUpFeignClientHelperExecuteRequest(FeignClientHelper feignClientHelper) throws TMBCommonException {
        lenient().when(feignClientHelper.executeRequest(any())).thenAnswer(invocation -> {
            try {
                Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
                ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
                return feignResponseEntity.getBody().getData();
            } catch (Exception e) {
                throw new TMBCommonException("");
            }
        });
    }

    public static void setUpFeignClientHelperExecuteRequestOrElseThrow(FeignClientHelper feignClientHelper) throws TMBCommonException {
        lenient().when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            try {
                ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
                return Objects.requireNonNull(feignResponseEntity.getBody()).getData();
            } catch (Exception e) {
                Supplier<TMBCommonException> errorSupplier = invocation.getArgument(1);
                throw errorSupplier.get();
            }
        });
    }

    public static void setUpFeignClientHelperExecuteRequestSafely(FeignClientHelper feignClientHelper) throws TMBCommonException {
        lenient().when(feignClientHelper.executeRequestSafely(any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
            return Objects.requireNonNull(feignResponseEntity.getBody()).getData();
        });
    }

    public static void setUpAsyncHelperExecuteMethodAsync(AsyncHelper asyncHelper) throws TMBCommonException {
        lenient().when(asyncHelper.executeMethodAsync(any())).thenAnswer(invocation -> {
            ThrowableSupplier<?> supplier = invocation.getArgument(0);
            try {
                Object result = supplier.get();
                return CompletableFuture.completedFuture(result);
            } catch (Exception e) {
                CompletableFuture<?> future = new CompletableFuture<>();
                future.completeExceptionally(e);
                return future;
            }
        });
    }

    public static void setUpAsyncHelperExecuteRequestAsyncSafely(AsyncHelper asyncHelper) {
        lenient().when(asyncHelper.executeRequestAsyncSafely(any())).thenAnswer(invocation -> {
            Supplier<?> supplier = invocation.getArgument(0);
            try {
                Object response = supplier.get();
                return CompletableFuture.completedFuture(response);
            } catch (Exception e) {
                CompletableFuture<Object> future = new CompletableFuture<>();
                future.completeExceptionally(e);
                return future;
            }
        });
    }

    public static void setUpAsyncHelperExecuteRequestAsync(AsyncHelper asyncHelper) throws TMBCommonException {
        lenient().when(asyncHelper.executeRequestAsync(any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
            return CompletableFuture.completedFuture(Objects.requireNonNull(feignResponseEntity.getBody()).getData());
        });
    }

    public static void setUpAsyncExecuteMethodAsyncSafelyVoid(AsyncHelper asyncHelper) {
        lenient().doAnswer(invocation -> {
            ThrowableRunnable runnable = invocation.getArgument(0, ThrowableRunnable.class);
            runnable.run();
            return null;  // Void method returns null
        }).when(asyncHelper).executeMethodAsyncSafelyVoid(any(ThrowableRunnable.class));
    }
}
