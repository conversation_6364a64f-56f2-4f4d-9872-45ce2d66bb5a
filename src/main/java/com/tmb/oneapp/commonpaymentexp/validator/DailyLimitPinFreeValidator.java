package com.tmb.oneapp.commonpaymentexp.validator;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonData;
import com.tmb.oneapp.commonpaymentexp.client.CommonServiceClient;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthForceFR;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.client.CommonAuthenticationService;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_PRE_LOGIN;


@Component
@RequiredArgsConstructor
public class DailyLimitPinFreeValidator {
    private static final TMBLogger<DailyLimitPinFreeValidator> logger = new TMBLogger<>(DailyLimitPinFreeValidator.class);
    private final CommonServiceClient commonServiceClient;
    private final CustomerServiceClient customerServiceClient;
    private final CommonAuthenticationService commonAuthenticationService;

    @Value("${fr.payment.accumulate.usage.limit:200000}")
    private BigDecimal totalPaymentAccumulateUsageLimit;

    public CommonAuthenResult validateIsRequireCommonAuthForTopUp(
            HttpHeaders headers,
            BigDecimal reqAmount,
            boolean isOwn,
            CustomerCrmProfile crmProfile
    ) throws TMBCommonException {
        boolean shouldValidatePaymentAccumulateUsageLimit = true;
        return this.validateIsRequireCommonAuth(headers, reqAmount, isOwn, crmProfile, crmProfile.getPinFreeBpLimit(), shouldValidatePaymentAccumulateUsageLimit);
    }

    public CommonAuthenResult validateIsRequireCommonAuthForBill(
            HttpHeaders headers,
            BigDecimal reqAmount,
            boolean isOwn,
            CustomerCrmProfile crmProfile
    ) throws TMBCommonException {
        return this.validateIsRequireCommonAuth(headers, reqAmount, isOwn, crmProfile, crmProfile.getPinFreeBpLimit(), false);
    }

    private CommonAuthenResult validateIsRequireCommonAuth(
            HttpHeaders headers,
            BigDecimal reqAmount,
            boolean isOwn,
            CustomerCrmProfile crmProfile,
            double pinFreeTranLimit,
            boolean shouldValidatePaymentAccumulateUsageLimit
    ) throws TMBCommonException {
        CommonAuthenResult response = new CommonAuthenResult();
        boolean isPreLogin = Boolean.parseBoolean(headers.getFirst(HEADER_PRE_LOGIN));

        if (isPreLogin) {
            response.setRequireCommonAuthen(true);
            return response;
        }

        if (isOwn) {
            response.setRequireCommonAuthen(false);
            return response;
        }

        boolean isPinFree = false;
        Boolean isDDP = null;
        boolean requireCommonAuthentication;
        String crmId = headers.getFirst(HEADER_CRM_ID);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        Integer pinFreeTranCount = crmProfile.getPinFreeTxnCount();
        boolean isPinFreeDisabled = ("N").equalsIgnoreCase(crmProfile.getPinFreeSeetingFlag());
        boolean amountMoreThanLimit = reqAmount.compareTo(BigDecimal.valueOf(pinFreeTranLimit)) > 0;

        if (isPinFreeDisabled || amountMoreThanLimit) {
            requireCommonAuthentication = true;
        } else if (isNotPinFree(pinFreeTranCount, correlationId)) {
            requireCommonAuthentication = true;
        } else if (validateCrmIDInDDP(crmId, correlationId)) {
            isPinFree = true;
            isDDP = true;
            requireCommonAuthentication = true;
        } else {
            isPinFree = true;
            isDDP = false;
            if (shouldValidatePaymentAccumulateUsageLimit) {
                BigDecimal totalUsage = crmProfile.getPaymentAccuUsgAmt().add(reqAmount);
                requireCommonAuthentication = totalUsage.compareTo(totalPaymentAccumulateUsageLimit) >= 0;
            } else {
                requireCommonAuthentication = false;
            }
        }

        response.setRequireCommonAuthen(requireCommonAuthentication);
        response.setIsDDP(isDDP);
        response.setPinFree(isPinFree);
        return response;
    }

    private boolean isNotPinFree(
            Integer pinFreeTxnCount,
            String correlationId
    ) throws TMBCommonException {
        CommonData commonConfig = FeignClientUtils.executeRequest(() -> commonServiceClient.fetchCommonConfig("common_module", correlationId)).get(0);

        Integer pinFreeMaxTrans = Integer.parseInt(commonConfig.getPinFreeMaxTrans());
        return pinFreeTxnCount >= pinFreeMaxTrans;
    }

    private boolean validateCrmIDInDDP(String crmId, String correlationId) {
        return Optional.ofNullable(commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId))
                .map(CommonAuthForceFR::getIsForce)
                .orElse(false);
    }
}
