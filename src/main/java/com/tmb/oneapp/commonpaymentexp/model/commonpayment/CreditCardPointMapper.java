package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CreditCardPoint;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CreditCardPointMapper {
    CreditCardPointMapper INSTANCE = Mappers.getMapper(CreditCardPointMapper.class);

    CreditCardPoint mapToCreditCardPoint(CreditCardSupplementary creditCardSupplementary);

    List<CreditCardPoint> mapToCreditCardPointList(List<CreditCardSupplementary> creditCardSupplementaryList);
}
