package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PromptPayETEConfirmRequestMapper {
    PromptPayETEConfirmRequestMapper INSTANCE = Mappers.getMapper(PromptPayETEConfirmRequestMapper.class);


    /**
     * Maps PromptPayETEValidateResponse to PromptPayETEConfirmRequest.
     * @param eteResponse PromptPayETEValidateResponse to be mapped.
     * @return PromptPayETEConfirmRequest object.
     */
    PromptPayETEConfirmRequest mapToPromptPayETEConfirmRequest(PromptPayETEValidateResponse eteResponse);
}
