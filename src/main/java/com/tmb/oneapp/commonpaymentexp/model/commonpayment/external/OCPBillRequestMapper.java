package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external;

import com.tmb.common.cache.Transaction;
import com.tmb.common.constants.TmbCommonUtilityConstants;
import com.tmb.common.model.BillerCreditcardMerchant;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanSender;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETETransaction;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan.AutoLoanExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan.AutoLoanPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static com.tmb.common.constants.TmbCommonUtilityConstants.ACCOUNT_TYPE_CCA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_ONLINE;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_AIS_ON_TOP;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_EASY_PASS;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_FLEET_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_PWA_BARCODE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.*;

public class OCPBillRequestMapper {
    public static final OCPBillRequestMapper INSTANCE = new OCPBillRequestMapper();

    private static final Integer ACCOUNT_ID_LENGTH_FOR_OCP_CONFIRM = 25;
    private static final String PEA_PAYMENT_ID_PREFIX = "M";
    private static final String MWA_PAYMENT_ID_PREFIX = "B011M";
    private static final int PEA_REFERENCE_LENGTH = 7;
    private static final int MWA_REFERENCE_LENGTH = 7;
    private static final String PEA_DATE_FORMAT = "ddMMyyyy";
    private static final String MWA_DATE_FORMAT = "ddMMyyyy";
    private static final String INTEREST_DATE = "InterestDate";
    private static final String PEA_REF3 = "MI";
    private static final String PEA_REF4 = "00";
    public static final String BILLER_TRAN_CODE_LOAN_POSTFIX = "5";
    public static final String ACCOUNT_TYPE_LOC = "LOC";
    public static final String LOAN_ACCOUNT_PREFIX = "0";

    private OCPBillRequestMapper() {
    }

    public OCPBillRequest toOCPBillRequestForValidateOCPBillPayment(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, CreditCardSupplementary creditCardDetail) {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isPayWithCreditCardFlag = request.getCreditCard().isPayWithCreditCardFlag();

        final String paymentId = this.getSequencePaymentId();

        final String ePayCode = this.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT);

        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));

        OCPBillRequest ocpRequest = new OCPBillRequest();
        ocpRequest.setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        ocpRequest.setChannel(OCP_REQUEST_CHANNEL);
        ocpRequest.setBankId(TTB_BANK_CODE_3DIGIT);
        ocpRequest.setBranchId(TOP_UP_BRANCH_ID);
        ocpRequest.setTellerId(BLANK);
        ocpRequest.setCurrency(TOP_UP_CURRENCY);
        ocpRequest.setRequestId(UUID.randomUUID().toString());
        ocpRequest.setRequestDateTime(requestDateTime);
        ocpRequest.setPaymentId(paymentId);
        ocpRequest.setBankRefId(paymentId);
        ocpRequest.setEpayCode(ePayCode);
        ocpRequest.setCompCode(compCode);
        ocpRequest.setRef3(BLANK);
        ocpRequest.setRef4(BLANK);

        ocpRequest.setRef1(cache.getPaymentInformation().getProductDetail().getProductRef1());
        ocpRequest.setRef2(cache.getPaymentInformation().getProductDetail().getProductRef2());

        if (StringUtils.isBlank(ocpRequest.getRef2()) && masterBillerResponse.getBillerInfo().getPaymentMethod().equals(BILLER_ONLINE)) {
            ocpRequest.setRef2(ocpRequest.getPaymentId());
        }

        if (compCode.equals(BILL_COMP_CODE_PWA_BARCODE)) {
            ocpRequest.setRef3(cache.getPaymentInformation().getProductDetail().getProductRef3());
        }

        if (isPayWithCreditCardFlag) {
            ocpRequest.setTranCode(CREDIT_CARD_TOPUP_TXN_CODE);
            ocpRequest.setBankId(TTB_BANK_CODE_3DIGIT);
            ocpRequest.setAmount(String.valueOf(request.getCreditCard().getAmount()));
        } else {
            ocpRequest.setAmount(String.valueOf(request.getDeposit().getAmount()));
        }

        ocpRequest.setFromAccount(getOCPFromAccount(fromDepositAccount, creditCardDetail, isPayWithCreditCardFlag));
        if (compCode.equals(BILL_COMP_CODE_FLEET_CARD)) {
            String tranCode = BILL_TRAN_CODE_PREFIX + TMBUtils.generateTransactionCode(fromDepositAccount.getAccountType()) + BILLER_TRAN_CODE_FLEET_CARD_POSTFIX;
            ocpRequest.setTranCode(tranCode);
        }

        if (masterBillerResponse.getBillerInfo().getBillerMethod().equals(METHOD_PRODUCT_OF_TMB)) {
            OCPAccountPayment toAccount = new OCPAccountPayment();
            toAccount.setAccountId(ocpRequest.getRef1());
            toAccount.setAccountType(TmbCommonUtilityConstants.ACCOUNT_TYPE_CCA);

            ocpRequest.setToAccount(toAccount);
        }

        setUpWhenTopUpTelco(request, compCode, ocpRequest);

        return ocpRequest;

    }

    public OCPBillRequest toOCPBillRequestForConfirmOCPBillPayment(ValidationCommonPaymentRequest request, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, BillPayExternalValidateResponse externalValidateResponse, BillPayPrepareDataValidate billPayPrepareDataValidate, WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest) {
        final OCPBillRequest ocpRequest = externalValidateResponse.getOcpRequest();
        final OCPBillPayment ocpDataResponse = externalValidateResponse.getOcpDataResponse();
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CreditCardSupplementary creditCardDetail = billPayPrepareDataValidate.getFromCreditCardDetail();
        final BillPayConfiguration billpayConfiguration = billPayPrepareDataValidate.getBillPayConfiguration();
        OCPBillRequest ocpConfirmRequest = new OCPBillRequest();
        BeanUtils.copyProperties(ocpRequest, ocpConfirmRequest);

        ocpConfirmRequest.setToAccount(ocpDataResponse.getToAccount());
        ocpConfirmRequest.setFee(ocpDataResponse.getFee());
        ocpConfirmRequest.setAdditionalParams(ocpDataResponse.getAdditionalParams());
        ocpConfirmRequest.setRef1(ocpDataResponse.getRef1());
        ocpConfirmRequest.setRef2(ocpDataResponse.getRef2());
        ocpConfirmRequest.setRef3(ocpDataResponse.getRef3());
        ocpConfirmRequest.setRef4(ocpDataResponse.getRef4());

        ocpConfirmRequest.setBankRefId(ocpDataResponse.getBankRefId());

        if (ocpConfirmRequest.getAdditionalParams() == null) {
            ocpConfirmRequest.setAdditionalParams(Collections.emptyList());
        }

        if (ocpConfirmRequest.getCompCode().equals(BILL_COMP_CODE_EASY_PASS)) {
            ocpConfirmRequest.setPmtRefIdent(ocpConfirmRequest.getRef1());
            ocpConfirmRequest.setInvoiceNum(ocpDataResponse.getBankRefId());
        }

        if (ocpDataResponse.getFee().getBillPmtFee() == null) {
            ocpDataResponse.getFee().setBillPmtFee(ocpDataResponse.getFee().getPaymentFee());
        }

        if (request.getCreditCard().isPayWithCreditCardFlag()) {
            ocpConfirmRequest.setCard(getOcpCard(creditCardDetail));
            ocpConfirmRequest.setMerchant(getOcpMerchant(compCode, billpayConfiguration.getBillerCreditcardMerchant()));
            ocpConfirmRequest.setAdditionalParams(getAdditionalParams(ocpConfirmRequest, masterBillerResponse));
            ocpConfirmRequest.setTranCode("");
            ocpConfirmRequest.getFromAccount().setAccountId(padAccountId(ocpConfirmRequest.getFromAccount().getAccountId()));
        }

        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            ocpConfirmRequest.setFee(new OCPFee().setFeeType("WOW").setBillPmtFee("0.00"));
            ocpConfirmRequest.setAdditionalParams(getWowPointAdditionalParams(ocpConfirmRequest, request, wowPointRedeemConfirmRequest));
        }

        return ocpConfirmRequest;
    }

    public OCPBillRequest toOCPBillRequestForValidateMEAOCPBillPayment(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, CommonPaymentDraftCache cache, CreditCardSupplementary creditCardDetail) {
        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isPayWithCreditCardFlag = request.getCreditCard().isPayWithCreditCardFlag();
        final String paymentId = this.getTransactionId(ONLINE_TRANS_REF_SEQUENCE, 9);

        OCPBillRequest ocpRequest = new OCPBillRequest();
        ocpRequest.setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        ocpRequest.setChannel(OCP_REQUEST_CHANNEL);
        ocpRequest.setBankId(TTB_BANK_CODE_3DIGIT);
        ocpRequest.setBranchId(TOP_UP_BRANCH_ID);
        ocpRequest.setTellerId(BLANK);
        ocpRequest.setCurrency(TOP_UP_CURRENCY);
        ocpRequest.setRequestId(UUID.randomUUID().toString());
        ocpRequest.setRequestDateTime(requestDateTime);
        ocpRequest.setPaymentId(paymentId);
        ocpRequest.setBankRefId(paymentId);
        ocpRequest.setCompCode(compCode);
        ocpRequest.setRef2(BLANK);
        ocpRequest.setRef3(BLANK);
        ocpRequest.setRef4(BLANK);

        ocpRequest.setRef1(cache.getPaymentInformation().getProductDetail().getProductRef1());

        if (isPayWithCreditCardFlag) {
            ocpRequest.setTranCode(CREDIT_CARD_TOPUP_TXN_CODE);
            ocpRequest.setAmount(String.valueOf(request.getCreditCard().getAmount()));
        } else {
            ocpRequest.setAmount(String.valueOf(request.getDeposit().getAmount()));
        }

        defaultAmount(ocpRequest);

        ocpRequest.setFromAccount(getOCPFromAccount(fromDepositAccount, creditCardDetail, isPayWithCreditCardFlag));

        return ocpRequest;
    }

    public OCPBillRequest toOCPBillRequestForValidatePEAOCPBillPayment(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, CommonPaymentDraftCache cache, CreditCardSupplementary creditCardDetail) {
        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isPayWithCreditCardFlag = request.getCreditCard().isPayWithCreditCardFlag();
        String paymentId = this.generatePEAPaymentId();

        OCPBillRequest ocpRequest = new OCPBillRequest();
        ocpRequest.setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        ocpRequest.setChannel(OCP_REQUEST_CHANNEL);
        ocpRequest.setBankId(TTB_BANK_CODE_3DIGIT);
        ocpRequest.setBranchId(TOP_UP_BRANCH_ID);
        ocpRequest.setTellerId(BLANK);
        ocpRequest.setCurrency(TOP_UP_CURRENCY);
        ocpRequest.setRequestId(UUID.randomUUID().toString());
        ocpRequest.setRequestDateTime(requestDateTime);
        ocpRequest.setPaymentId(paymentId);
        ocpRequest.setBankRefId(paymentId);
        ocpRequest.setCompCode(compCode);
        ocpRequest.setRef2(BLANK);
        ocpRequest.setRef3(PEA_REF3);
        ocpRequest.setRef4(PEA_REF4);

        ocpRequest.setRef1(cache.getPaymentInformation().getProductDetail().getProductRef1());

        if (isPayWithCreditCardFlag) {
            ocpRequest.setTranCode(CREDIT_CARD_TOPUP_TXN_CODE);
            ocpRequest.setAmount(String.valueOf(request.getCreditCard().getAmount()));
        } else {
            ocpRequest.setAmount(String.valueOf(request.getDeposit().getAmount()));
        }

        defaultAmount(ocpRequest);

        ocpRequest.setFromAccount(getOCPFromAccount(fromDepositAccount, creditCardDetail, isPayWithCreditCardFlag));

        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName(INTEREST_DATE);
        additionalParam.setValue(requestDateTime);

        ocpRequest.setAdditionalParams(List.of(additionalParam));

        return ocpRequest;
    }

    public OCPBillRequest toOCPBillRequestForValidateMWAOCPBillPayment(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, CommonPaymentDraftCache cache, CreditCardSupplementary creditCardDetail) {
        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isPayWithCreditCardFlag = request.getCreditCard().isPayWithCreditCardFlag();
        String paymentId = this.generateMWAPaymentId();

        OCPBillRequest ocpRequest = new OCPBillRequest();
        ocpRequest.setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        ocpRequest.setChannel(OCP_REQUEST_CHANNEL);
        ocpRequest.setBankId(TTB_BANK_CODE_3DIGIT);
        ocpRequest.setBranchId(TOP_UP_BRANCH_ID);
        ocpRequest.setTellerId(BLANK);
        ocpRequest.setCurrency(TOP_UP_CURRENCY);
        ocpRequest.setRequestId(UUID.randomUUID().toString());
        ocpRequest.setRequestDateTime(requestDateTime);
        ocpRequest.setPaymentId(paymentId);
        ocpRequest.setBankRefId(paymentId);
        ocpRequest.setCompCode(compCode);
        ocpRequest.setRef2(BLANK);
        ocpRequest.setRef3(PEA_REF3);
        ocpRequest.setRef4(PEA_REF4);

        ocpRequest.setRef1(cache.getPaymentInformation().getProductDetail().getProductRef1());

        if (isPayWithCreditCardFlag) {
            ocpRequest.setTranCode(CREDIT_CARD_TOPUP_TXN_CODE);
            ocpRequest.setAmount(String.valueOf(request.getCreditCard().getAmount()));
        } else {
            ocpRequest.setAmount(String.valueOf(request.getDeposit().getAmount()));
        }

        defaultAmount(ocpRequest);

        ocpRequest.setFromAccount(getOCPFromAccount(fromDepositAccount, creditCardDetail, isPayWithCreditCardFlag));

        AdditionalParam additionalParam = new AdditionalParam();
        additionalParam.setName(INTEREST_DATE);
        additionalParam.setValue(requestDateTime);

        ocpRequest.setAdditionalParams(List.of(additionalParam));

        return ocpRequest;
    }

    public OCPBillRequest toOCPBillRequestForConfirmCustomOCPBillPayment(ValidationCommonPaymentRequest request, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, BillPayExternalValidateResponse billPayExternalValidateResponse, BillPayPrepareDataValidate billPayPrepareDataValidate, WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest) {
        final OCPBillRequest ocpRequest = billPayExternalValidateResponse.getOcpRequest();
        final OCPBillPayment ocpDataResponse = billPayExternalValidateResponse.getOcpDataResponse();
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CreditCardSupplementary creditCardDetail = billPayPrepareDataValidate.getFromCreditCardDetail();
        final BillPayConfiguration billpayConfiguration = billPayPrepareDataValidate.getBillPayConfiguration();
        final DepositAccount fromDepositAccount = billPayPrepareDataValidate.getFromDepositAccount();
        final String ePayCode = this.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT);
        final boolean isPayWithCreditCardFlag = request.getCreditCard().isPayWithCreditCardFlag();

        OCPBillRequest ocpConfirmRequest = new OCPBillRequest();
        BeanUtils.copyProperties(ocpRequest, ocpConfirmRequest);

        ocpConfirmRequest.setToAccount(ocpDataResponse.getToAccount());
        ocpConfirmRequest.setFee(ocpDataResponse.getFee());
        setFee(fromDepositAccount, ocpConfirmRequest, isPayWithCreditCardFlag);
        ocpConfirmRequest.setEpayCode(ePayCode);
        ocpConfirmRequest.setAdditionalParams(ocpDataResponse.getAdditionalParams());
        ocpConfirmRequest.setRef1(ocpDataResponse.getRef1());
        ocpConfirmRequest.setRef2(ocpDataResponse.getRef2());
        ocpConfirmRequest.setRef3(ocpDataResponse.getRef3());
        ocpConfirmRequest.setRef4(ocpDataResponse.getRef4());

        ocpConfirmRequest.setBankRefId(ocpDataResponse.getBankRefId());
        ocpConfirmRequest.setPaymentId(ocpDataResponse.getPaymentId());

        if (ocpConfirmRequest.getAdditionalParams() == null) {
            ocpConfirmRequest.setAdditionalParams(Collections.emptyList());
        }

        if (request.getCreditCard().isPayWithCreditCardFlag()) {
            ocpConfirmRequest.setCard(getOcpCard(creditCardDetail));
            ocpConfirmRequest.setMerchant(getOcpMerchant(compCode, billpayConfiguration.getBillerCreditcardMerchant()));
            ocpConfirmRequest.setAdditionalParams(getAdditionalParams(ocpConfirmRequest, masterBillerResponse));
            ocpConfirmRequest.getFromAccount().setAccountId(padAccountId(ocpConfirmRequest.getFromAccount().getAccountId()));
            ocpConfirmRequest.setTranCode(null);
        }

        if (ocpDataResponse.getBalance() != null) {
            ocpConfirmRequest.setBalance(ocpDataResponse.getBalance());
        }

        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            ocpConfirmRequest.setFee(new OCPFee().setFeeType("WOW").setBillPmtFee("0.00"));
            ocpConfirmRequest.setAdditionalParams(getWowPointAdditionalParams(ocpConfirmRequest, request, wowPointRedeemConfirmRequest));
        }

        ocpConfirmRequest.setInvoiceNum(ocpDataResponse.getAdditionalParams().stream()
                .filter(param -> StringUtils.equals(param.getName(), "InvoiceNum"))
                .findFirst()
                .map(AdditionalParam::getValue)
                .map(Objects::toString)
                .orElse(null));

        return ocpConfirmRequest;
    }

    public OCPBillRequest toOCPBillRequestForValidateLoanPayment(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, CommonPaymentDraftCache cache) {
        final String loanAccountId = LOAN_ACCOUNT_PREFIX + cache.getPaymentInformation().getProductDetail().getProductRef1() + cache.getPaymentInformation().getProductDetail().getProductRef2();
        final String compCode = cache.getPaymentInformation().getCompCode();

        String paymentId = this.getSequencePaymentId();
        String ePayCode = this.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT);
        String tranCode = BILL_TRAN_CODE_PREFIX + TMBUtils.generateTransactionCode(fromDepositAccount.getAccountType()) + BILLER_TRAN_CODE_LOAN_POSTFIX;

        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));

        OCPBillRequest ocpRequest = new OCPBillRequest();
        ocpRequest.setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        ocpRequest.setChannel(OCP_REQUEST_CHANNEL);
        ocpRequest.setBankId(TTB_BANK_CODE_3DIGIT);
        ocpRequest.setBranchId(TOP_UP_BRANCH_ID);
        ocpRequest.setTellerId(BLANK);
        ocpRequest.setCurrency(TOP_UP_CURRENCY);
        ocpRequest.setRequestId(UUID.randomUUID().toString());
        ocpRequest.setRequestDateTime(requestDateTime);
        ocpRequest.setPaymentId(paymentId);
        ocpRequest.setBankRefId(paymentId);
        ocpRequest.setEpayCode(ePayCode);
        ocpRequest.setCompCode(compCode);

        ocpRequest.setRef1(loanAccountId);
        ocpRequest.setRef2(BLANK);
        ocpRequest.setTranCode(tranCode);

        ocpRequest.setAmount(String.valueOf(request.getDeposit().getAmount()));

        OCPAccountPayment fromAccount = new OCPAccountPayment();
        fromAccount.setFiId(BLANK);
        fromAccount.setAccountId(fromDepositAccount.getAccountNumber());
        fromAccount.setAccountType(fromDepositAccount.getAccountType());
        ocpRequest.setFromAccount(fromAccount);

        OCPAccountPayment toAccount = new OCPAccountPayment();
        toAccount.setAccountId(loanAccountId);
        toAccount.setAccountType(ACCOUNT_TYPE_LOC);
        toAccount.setFiId(BLANK);
        ocpRequest.setToAccount(toAccount);

        return ocpRequest;
    }

    public OCPBillRequest toOCPBillRequestForConfirmLoanPayment(OCPBillPayment eteResponse, DepositAccount fromDepositAccount, ValidationCommonPaymentRequest validateRequest, CommonPaymentDraftCache cache, WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest) {
        boolean isWaiveFeeFromDepositAccount = StringUtils.equals("1", fromDepositAccount.getWaiveFeeForBillpay());

        OCPBillRequest confirmRequest = OCPBillPaymentMapper.INSTANCE.mapToOCPRequest(eteResponse);

        if (isWaiveFeeFromDepositAccount) {
            confirmRequest.getFee().setBillPmtFee("0.00");
        } else {
            String replacePaymentFee = confirmRequest.getFee().getPaymentFee();
            confirmRequest.getFee().setBillPmtFee(replacePaymentFee);
        }

        if (WowPointUtils.isWowPointTransaction(validateRequest, cache.getCommonPaymentRule())) {
            confirmRequest.setFee(new OCPFee().setFeeType("WOW").setBillPmtFee("0.00"));
            confirmRequest.setAdditionalParams(getWowPointAdditionalParams(confirmRequest, validateRequest, wowPointRedeemConfirmRequest));
        }

        return confirmRequest;
    }

    public AutoLoanOCPBillRequest toAutoLoanOCPBillRequestForConfirmAutoLoanWithWowPayment(ValidationCommonPaymentRequest validateRequest, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, AutoLoanExternalValidateResponse externalResponse, AutoLoanPrepareDataValidate prepareData, WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest) {
        TopUpETETransaction transaction = externalResponse.getTopUpETEResponse().getTransaction();
        final String paymentId = externalResponse.getTopUpETEPaymentRequest().getPaymentId();
        final String ePayCode = this.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT);
        final String compCode = cache.getPaymentInformation().getCompCode();

        SimpleDateFormat date = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        String requestDateTime = date.format(new Date(System.currentTimeMillis()));

        AutoLoanOCPBillRequest confirmRequest = new AutoLoanOCPBillRequest();
        confirmRequest.setAppId(REQUEST_APP_ID_OCP_GATEWAY_VALUE);
        confirmRequest.setChannel(OCP_REQUEST_CHANNEL);
        confirmRequest.setBankId(TTB_BANK_CODE_3DIGIT);
        confirmRequest.setBranchId(TOP_UP_BRANCH_ID);
        confirmRequest.setTellerId(BLANK);
        confirmRequest.setCurrency(TOP_UP_CURRENCY);
        confirmRequest.setRequestId(UUID.randomUUID().toString());
        confirmRequest.setRequestDateTime(requestDateTime);
        confirmRequest.setPaymentId(paymentId);
        confirmRequest.setBankRefId(paymentId);
        confirmRequest.setEpayCode(ePayCode);
        confirmRequest.setCompCode(compCode);

        confirmRequest.setAmount(String.valueOf(validateRequest.getDeposit().getAmount()));

        confirmRequest.setRef1(transaction.getReference1());
        confirmRequest.setRef2(transaction.getReference2());
        confirmRequest.setRef3(transaction.getReference3());
        confirmRequest.setRef4(transaction.getReference4());
        confirmRequest.setTranCode(BILL_TRAN_CODE_PREFIX + TMBUtils.generateTransactionCode(prepareData.getFromDepositAccount().getAccountType()) + BILLER_TRAN_CODE_AUTO_LOAN_SUFFIX);

        OCPAccountPayment fromAccount = new OCPAccountPayment();
        fromAccount.setFiId(BLANK);
        fromAccount.setAccountId(prepareData.getFromDepositAccount().getAccountNumber());
        fromAccount.setAccountType(prepareData.getFromDepositAccount().getAccountType());
        confirmRequest.setFromAccount(fromAccount);

        OCPAccountPayment toAccount = new OCPAccountPayment();
        String billerAccountId = masterBillerResponse.getBillerInfo().getToAccountId();
        toAccount.setAccountId(billerAccountId);
        toAccount.setAccountType(TMBUtils.getAccountType(billerAccountId));
        confirmRequest.setToAccount(toAccount);

        String customerName = transaction.getTbankCustomer().getName();

        AutoLoanDetail autoLoanDetail = new AutoLoanDetail();
        autoLoanDetail.setAppId(BLANK);
        autoLoanDetail.setHpMode(BLANK);
        autoLoanDetail.setChannelId("MB");
        autoLoanDetail.setLocation("MB");
        autoLoanDetail.setTerminalId("MB");
        autoLoanDetail.setCustomerName(customerName);
        String traceNo = paymentId.substring(paymentId.length() - 9);
        autoLoanDetail.setTraceNo(traceNo);
        confirmRequest.setAutoLoan(autoLoanDetail);

        AutoLoanSender sender = new AutoLoanSender();
        CustomerKYCResponse customerKyc = prepareData.getCustomerKYCResponse();
        String fullName = customerKyc.getCustomerFirstNameEn() + " " + customerKyc.getCustomerLastNameEn();
        sender.setFullName(fullName);
        confirmRequest.setSender(sender);
        confirmRequest.setAdditionalParams(Collections.singletonList(new AdditionalParam().setName("TBankCustomerName").setValue(customerName)));

        confirmRequest.setFee(new OCPFee().setFeeType("WOW").setBillPmtFee("0.00"));
        confirmRequest.setAdditionalParams(getWowPointAdditionalParams(confirmRequest, validateRequest, wowPointRedeemConfirmRequest));

        return confirmRequest;
    }

    private void setFee(DepositAccount fromDepositAccount, OCPBillRequest ocpConfirmRequest, boolean isPayWithCreditCardFlag) {
        if (isPayWithCreditCardFlag || fromDepositAccount.getWaiveFeeForBillpay().equals("1")) {
            OCPFee ocpFee = ocpConfirmRequest.getFee();
            Optional.ofNullable(ocpFee).ifPresentOrElse(
                    reqFee -> ocpConfirmRequest.getFee().setBillPmtFee("0.00"),
                    () -> ocpConfirmRequest.setFee(new OCPFee().setBillPmtFee("0.00"))
            );
        }
    }

    private void defaultAmount(OCPBillRequest ocpRequest) {
        if (Strings.isBlank(ocpRequest.getAmount())) {
            ocpRequest.setAmount("0.00");
        }
    }

    private ArrayList<AdditionalParam> getAdditionalParams(OCPBillRequest ocpBillPayment, MasterBillerResponse masterBillerResponse) {
        ArrayList<AdditionalParam> customAdditionalParam = new ArrayList<>(ocpBillPayment.getAdditionalParams());
        customAdditionalParam.add(new AdditionalParam().setName("billerName").setValue(masterBillerResponse.getBillerInfo().getNameEn()));
        return customAdditionalParam;
    }

    private ArrayList<AdditionalParam> getWowPointAdditionalParams(OCPBillRequest ocpBillPayment, ValidationCommonPaymentRequest request, WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest) {
        ArrayList<AdditionalParam> wowPointAdditionalParam = new ArrayList<>();
        wowPointAdditionalParam.add(new AdditionalParam().setName("rm_id").setValue(wowPointRedeemConfirmRequest.getFromAccount().getRmId()));
        wowPointAdditionalParam.add(new AdditionalParam().setName("serial_code").setValue(wowPointRedeemConfirmRequest.getRewardCode()));
        wowPointAdditionalParam.add(new AdditionalParam().setName("cbs_ref4").setValue(String.valueOf(request.getWowPoint().getDiscountAmount())));
        if (ocpBillPayment.getAdditionalParams() != null) {
            ArrayList<AdditionalParam> customAdditionalParam = new ArrayList<>(ocpBillPayment.getAdditionalParams());
            customAdditionalParam.addAll(wowPointAdditionalParam);
            return customAdditionalParam;
        }
        return wowPointAdditionalParam;
    }

    private OCPMerchant getOcpMerchant(String compCode, List<BillerCreditcardMerchant> billerCreditCardMerchant) {
        OCPMerchant merchant = new OCPMerchant();
        final String merchantId = billerCreditCardMerchant.stream().filter(b -> StringUtils.equals(b.getBillerCompCode(), compCode)).findFirst().map(BillerCreditcardMerchant::getMerchantId).orElse(BLANK);
        merchant.setMerchantId(merchantId);
        return merchant;
    }

    private OCPCard getOcpCard(CreditCardSupplementary creditCardDetail) {
        OCPCard card = new OCPCard();
        card.setCardId(creditCardDetail.getCardNo());
        card.setCardExpiry(creditCardDetail.getExpiredBy());
        card.setCardEmbossingName(creditCardDetail.getCardEmbossingName1());
        card.setProductId(creditCardDetail.getProductCode());
        return card;
    }

    private void setUpWhenTopUpTelco(ValidationCommonPaymentRequest request, String compCode, OCPBillRequest ocpRequest) {
        boolean isAisOnTop = StringUtils.equals(compCode, BILL_COMP_CODE_AIS_ON_TOP);
        boolean isTrueMoveHDataPackage = StringUtils.equals(compCode, TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE);
        if (isAisOnTop || isTrueMoveHDataPackage) {
            AdditionalParam billId = new AdditionalParam().setName(BILL_ID_NAME).setValue(isTrueMoveHDataPackage ? ocpRequest.getRef2() : request.getTopUpTelco().getBillId());

            ocpRequest.setAdditionalParams(Collections.singletonList(billId));
            ocpRequest.setRef1(isTrueMoveHDataPackage ? ocpRequest.getRef1() : BILL_REF_1_ETE_REQUEST_AIS_ON_TOP);
        }
    }

    public String getSequencePaymentId() {
        String transactionId = getTransactionId(ONLINE_TRANS_REF_SEQUENCE, 5);
        transactionId = transactionId.substring(transactionId.length() - 5);

        SimpleDateFormat dateFormat = new SimpleDateFormat(DATETIME_FORMAT);
        String requestDate = dateFormat.format(new Date(System.currentTimeMillis()));
        return requestDate + transactionId;
    }

    public String getTransactionId(String key, int digits) {
        return Transaction.getTransactionId(key, digits);
    }

    private OCPAccountPayment getOCPFromAccount(DepositAccount depositAccount, CreditCardSupplementary creditCardDetail, boolean isPayWithCreditCardFlag) {
        OCPAccountPayment fromAccount = new OCPAccountPayment();
        fromAccount.setFiId(BLANK);
        if (isPayWithCreditCardFlag) {
            fromAccount.setAccountId(filterAccountNumber(creditCardDetail.getAccountId()));
            fromAccount.setAccountType(ACCOUNT_TYPE_CCA);
        } else {
            fromAccount.setAccountId(filterAccountNumber(depositAccount.getAccountNumber()));
            fromAccount.setAccountType(depositAccount.getAccountType());
        }
        return fromAccount;
    }

    private String filterAccountNumber(String accountId) {
        int check = accountId.length();
        int num = 24;
        if (check > 24) {
            return accountId.substring(check - num, check);
        }
        return accountId;
    }

    private String padAccountId(String accountId) {
        return StringUtils.leftPad(accountId, ACCOUNT_ID_LENGTH_FOR_OCP_CONFIRM, "0");
    }

    private String generatePEAPaymentId() {
        String reference = getTransactionId(PEA_TRANS_REF_SEQUENCE, 9);
        reference = reference.substring(reference.length() - PEA_REFERENCE_LENGTH);
        String dateStr = new SimpleDateFormat(PEA_DATE_FORMAT).format(new Date());
        return PEA_PAYMENT_ID_PREFIX + dateStr + reference;
    }

    private String generateMWAPaymentId() {
        String reference = getTransactionId(ONLINE_TRANS_REF_SEQUENCE, 9);
        reference = reference.substring(reference.length() - MWA_REFERENCE_LENGTH);
        String dateStr = new SimpleDateFormat(MWA_DATE_FORMAT).format(new Date());

        return MWA_PAYMENT_ID_PREFIX + dateStr + reference;
    }
}
