package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.BaseActivityBillPayEvent;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.util.Arrays;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_PAYMENT_METHOD_DEPOSIT;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_MOBILE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BILL_PAYMENT_ACTIVITY_CONFIRM_STEP;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CATEGORY_E_DONATION_ID;

public class ActivityBillPromptPayConfirmationEvent extends BaseActivityBillPayEvent {
    public ActivityBillPromptPayConfirmationEvent(String activityTypeId, HttpHeaders headers, CommonPaymentDraftCache cache) {
        super(activityTypeId, headers);
        setUpFields(cache);
    }

    private void setUpFields(CommonPaymentDraftCache cache) {
        final String transactionReference = cache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest().getTransactionReference();
        final String fromAccountNumber = cache.getValidateDraftCache().getFromDepositAccount().getAccountNumber();
        final String productRef1 = cache.getPaymentInformation().getProductDetail().getProductRef1();
        final String productRef2 = cache.getPaymentInformation().getProductDetail().getProductRef2();
        final String billerCategoryCode = cache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerCategoryCode();
        final Boolean isMobileFromMasterBiller = cache.getValidateDraftCache().getMasterBillerResponse().getRef1().getIsMobile();
        final Boolean isAllowShareToRdFlag = NullSafeUtils.getSafeNull(() -> cache.getValidateRequest().getEDonation().isAllowShareToRdFlag());
        final String billerNameEN = cache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getNameEn();
        final String compCode = cache.getPaymentInformation().getCompCode();
        final BigDecimal amountFromValidateRequest = cache.getValidateRequest().getDeposit().getAmount();
        final BigDecimal feeCalculated = cache.getValidateDraftCache().getFeeCalculated();
        final BigDecimal totalAmountFromCache = cache.getValidateDraftCache().getTotalAmount();
        final String overrideFlow = getOverrideFlow(cache);

        boolean isShouldMaskingRef1 = Arrays.asList(BILLER_CATEGORY_CODE_CREDIT_CARD, BILLER_CATEGORY_CODE_MOBILE).contains(billerCategoryCode) || Boolean.TRUE.equals(isMobileFromMasterBiller);
        boolean isShouldMaskingRef2 = StringUtils.equals(CATEGORY_E_DONATION_ID, billerCategoryCode) && Boolean.TRUE.equals(isAllowShareToRdFlag);

        super.fromAccount = fromAccountNumber;
        super.paymentMethod = ACTIVITY_PAYMENT_METHOD_DEPOSIT;
        super.step = BILL_PAYMENT_ACTIVITY_CONFIRM_STEP;
        super.reference1 = productRef1;
        super.reference2 = productRef2;
        super.flow = overrideFlow;
        super.refNo = transactionReference;

        super.amount = insertCommas(amountFromValidateRequest);
        super.fee = insertCommas(feeCalculated);
        super.totalAmount = insertCommas(totalAmountFromCache);

        super.couponDiscount = "-";
        super.wowPoint = "-";
        super.creditCardPoint = "-";

        super.ddp = "-";
        super.pinFree = "-";

        super.billerName = super.generateActivityBillerName(billerNameEN, compCode);

        if (isShouldMaskingRef1) {
            super.maskingReference1();
        }

        if (isShouldMaskingRef2) {
            super.maskingReference2();
        }
    }

}
