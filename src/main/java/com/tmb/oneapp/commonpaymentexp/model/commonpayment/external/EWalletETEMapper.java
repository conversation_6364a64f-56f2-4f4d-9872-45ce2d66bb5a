package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external;

import com.tmb.common.cache.Transaction;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ewallet.EWalletPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Receiver;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Sender;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Terminal;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.ALPHABET_I;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.B011B;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.BLANK;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.PROMPTPAY_REF_SEQ;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.PROXY_TYPE_E_WALLET;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.SENDER_TYPE_KEY_IN;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TERMINAL_TYPE;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TMBO;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TTB_BANK_CODE;

public class EWalletETEMapper {
    public static final EWalletETEMapper INSTANCE = new EWalletETEMapper();


    private EWalletETEMapper() {
    }

    public EWalletETERequest toEWalletETERequestForValidateEWalletPayment(ValidationCommonPaymentRequest request, EWalletPrepareDataValidate prepareData, CommonPaymentDraftCache cache) {
        final String reference1 = cache.getPaymentInformation().getProductDetail().getProductRef1();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final String taxId = Optional.ofNullable(prepareData.getCustomerKYC()).map(CustomerKYCResponse::getIdNo).orElse(BLANK);

        Sender sender = new Sender();
        sender.setAccountId(fromDepositAccount.getAccountNumber());
        sender.setAccountType(fromDepositAccount.getAccountType());
        sender.setAccountLength(fromDepositAccount.getAccountNumber().length());
        sender.setAccountName(fromDepositAccount.getAccountName());
        sender.setBankCode(TTB_BANK_CODE);
        sender.setTaxId(taxId);

        Receiver receiver = new Receiver();
        receiver.setProxyType(PROXY_TYPE_E_WALLET);
        receiver.setProxyValue(reference1);
        receiver.setAccountId(reference1);
        receiver.setAccountType(PROXY_TYPE_E_WALLET);
        receiver.setAccountLength(reference1.length());

        Terminal terminal = new Terminal();
        terminal.setId(this.generateTerminalId());
        terminal.setType(TERMINAL_TYPE);

        EWalletETERequest promptPayCallETERequest = new EWalletETERequest();
        promptPayCallETERequest.setAmount(String.valueOf(request.getDeposit().getAmount()));
        promptPayCallETERequest.setRtpTransactionReference(BLANK);
        ZonedDateTime time = ZonedDateTime.now();
        promptPayCallETERequest.setTransactionCreatedDatetime(time.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));

        promptPayCallETERequest.setSenderType(SENDER_TYPE_KEY_IN);

        promptPayCallETERequest.setTransactionReference(this.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, 8));

        promptPayCallETERequest.setReceiver(receiver);
        promptPayCallETERequest.setSender(sender);
        promptPayCallETERequest.setTerminal(terminal);

        return promptPayCallETERequest;
    }

    public EWalletETERequest toEWalletETERequestForConfirmEWalletPayment(final EWalletETEResponse externalValidateResponse) {
        EWalletETERequest confirmRequest = new EWalletETERequest();
        confirmRequest.setSender(externalValidateResponse.getSender());
        confirmRequest.getSender().setBankCode(TTB_BANK_CODE);
        confirmRequest.setTerminal(externalValidateResponse.getTerminal());
        confirmRequest.setReceiver(externalValidateResponse.getReceiver());
        confirmRequest.setAmount(externalValidateResponse.getAmount().toString());
        confirmRequest.setRtpTransactionReference(externalValidateResponse.getRtpTransactionReference());
        confirmRequest.setTransactionReference(externalValidateResponse.getTransactionReference());
        confirmRequest.setTransactionCreatedDatetime(externalValidateResponse.getTransactionCreatedDatetime());
        confirmRequest.setSenderType(externalValidateResponse.getSenderType());
        confirmRequest.setFee(externalValidateResponse.getFee().doubleValue());
        confirmRequest.setReceiverType(externalValidateResponse.getReceiverType());
        confirmRequest.setChargeCode(externalValidateResponse.getChargeCode());
        confirmRequest.setEffectiveDate(externalValidateResponse.getTransactionCreatedDatetime());
        confirmRequest.setChargeType("");

        return confirmRequest;
    }

    private String generateTerminalId() {
        String sequenceKey = Transaction.getSequenceKey(PROMPTPAY_REF_SEQ, 6);
        return ALPHABET_I + sequenceKey + B011B + TMBO;
    }

    private String getTransactionId(String key, int digits) {
        return Transaction.getTransactionId(key, digits);
    }
}
