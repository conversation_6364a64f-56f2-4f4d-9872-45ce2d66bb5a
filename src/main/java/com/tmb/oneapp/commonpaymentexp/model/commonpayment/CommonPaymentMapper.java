package com.tmb.oneapp.commonpaymentexp.model.commonpayment;


import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Mapper
public interface CommonPaymentMapper {
    CommonPaymentMapper INSTANCE = Mappers.getMapper(CommonPaymentMapper.class);

    @Mapping(target = "paymentInformation.amountDetail.amountValue", qualifiedByName = "set2DecimalRoundingDown")
    @Mapping(target = "crmId", ignore = true)
    @Mapping(target = "validateRequest", ignore = true)
    @Mapping(target = "partnerName", source = "partnerName")
    CommonPaymentDraftCache toCommonPaymentDraftCache(PaymentInformation paymentInformation, String processorType, InitialPrepareData prepareData, String partnerName);

    @Named("set2DecimalRoundingDown")
    default BigDecimal set2DecimalRoundingDown(final BigDecimal amountValue) {
        if (amountValue == null) {
            return null;
        }

        return amountValue.setScale(2, RoundingMode.DOWN);
    }
}
