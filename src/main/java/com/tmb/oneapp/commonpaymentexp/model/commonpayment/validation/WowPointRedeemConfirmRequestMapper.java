package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation;

import com.tmb.common.logger.TMBLogger;
import org.apache.commons.lang3.RandomStringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.DATETIME_FORMAT;

public class WowPointRedeemConfirmRequestMapper {
    private static final TMBLogger<WowPointRedeemConfirmRequestMapper> logger = new TMBLogger<>(WowPointRedeemConfirmRequestMapper.class);
    public static final WowPointRedeemConfirmRequestMapper INSTANCE = new WowPointRedeemConfirmRequestMapper();
    public static final String TTB_TOUCH_WOW_POINT = "TTB Touch";
    public static final String REDEEM_WOW_POINT = "REDEEM";
    private final SimpleDateFormat timeFormatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");

    private WowPointRedeemConfirmRequestMapper() {
    }

    public WowPointRedeemConfirmRequest mapToWowPointRedeemConfirmRequestForOCPAndCustomBill(ValidationCommonPaymentRequest request, String compCode, String crmId) {
        return new WowPointRedeemConfirmRequest()
                .setActivityId(compCode)
                .setCampaignId(compCode)
                .setChannel(TTB_TOUCH_WOW_POINT)
                .setAmount(request.getWowPoint().getDiscountAmount())
                .setPointUnits(request.getWowPoint().getWowPointAmount())
                .setTxnAmount(request.getWowPoint().getTxnAmount())
                .setTransactionType(REDEEM_WOW_POINT)
                .setTransactionDatetime(timeFormatter.format(new Date()))
                .setRewardCode(this.generateLoyaltyRewardCode(compCode))
                .setFromAccount(new FromWowAccount().setRmId(crmId));
    }

    private String generateLoyaltyRewardCode(String compCode) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATETIME_FORMAT);
        String requestDate = dateFormat.format(new Date(System.currentTimeMillis()));

        return compCode + requestDate + RandomStringUtils.randomAlphanumeric(6);
    }
}
