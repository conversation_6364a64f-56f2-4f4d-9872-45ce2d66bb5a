package com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InitializationCommonPaymentRequest {
    @NotNull(message = "Payment Information is required")
    @Valid
    private PaymentInformation paymentInformation;
}
