package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PaymentStatus;
import com.tmb.oneapp.commonpaymentexp.service.PaymentStatusPublisherService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CallbackConfirmService {
    private static final TMBLogger<CallbackConfirmService> logger = new TMBLogger<>(CallbackConfirmService.class);
    private static final String PB_ENTRY_ID = "pb";
    private final PaymentStatusPublisherService paymentStatusPublisherService;

    public void callback(CommonPaymentDraftCache draftCache, PaymentStatus paymentStatus) {
        if (paymentStatus == null) {
            logger.warn("PaymentStatus is null, skipping publish.");
            return;
        }

        final String partnerName = draftCache.getPartnerName();
        final String entryId = draftCache.getPaymentInformation().getEntryId();

        if (StringUtils.equalsIgnoreCase(PB_ENTRY_ID, entryId)) {
            logger.debug("bypass kafka cause entry id = pb");
            return;
        }

        final boolean isFromPartner = StringUtils.isNotBlank(partnerName);

        final String identifier;
        if (isFromPartner) {
            logger.debug("Publishing payment status for transactionId: {} to partner name: {}", paymentStatus.getTransactionId(), partnerName);
            identifier = partnerName;
        } else {
            logger.debug("Publishing payment status for transactionId: {}", paymentStatus.getTransactionId());
            identifier = entryId;
        }
        paymentStatusPublisherService.publish(paymentStatus, identifier);
    }
}
