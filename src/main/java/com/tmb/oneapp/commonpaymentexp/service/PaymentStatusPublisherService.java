package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PaymentStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class PaymentStatusPublisherService {
    private static final TMBLogger<PaymentStatusPublisherService> logger = new TMBLogger<>(PaymentStatusPublisherService.class);
    private final KafkaProducerService kafkaProducerService;
    private final String paymentStatusTopic;


    public PaymentStatusPublisherService(KafkaProducerService kafkaProducerService,
                                         @Value("${com.tmb.oneapp.common.payment.exp.payment.status}") final String paymentStatusTopic) {
        this.kafkaProducerService = kafkaProducerService;
        this.paymentStatusTopic = paymentStatusTopic;
    }

    public void publish(PaymentStatus paymentStatus, String identifier) {
        try {
            String payload = TMBUtils.convertJavaObjectToString(paymentStatus);
            Map<String, String> headers = new HashMap<>();
            headers.put("entry_id", identifier);
            logger.debug("Publishing payment status to kafka topic: {} with headers: {} and payload: {}", paymentStatusTopic, headers, payload);
            kafkaProducerService.sendMessageAsync(paymentStatusTopic, "", payload, headers);
            logger.debug("Successfully published payment status for transactionId: {}", paymentStatus.getTransactionId());
        } catch (Exception e) {
            logger.error("Error publishing payment status for transactionId: {}", paymentStatus.getTransactionId(), e);
        }
    }
}