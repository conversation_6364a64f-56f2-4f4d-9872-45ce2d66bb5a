package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.ProcessorSelectorUtils;
import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.UUID;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.PROCESSOR_SCHEDULE_UNIMPLEMENTED;
import static com.tmb.oneapp.commonpaymentexp.utils.ProcessorSelectorUtils.isTransactionTypeBillPay;

@Service
public class InitializationCommonPaymentService {
    private static final TMBLogger<InitializationCommonPaymentService> logger =
        new TMBLogger<>(InitializationCommonPaymentService.class);
    private final CacheService cacheService;
    private final PaymentService paymentService;
    private final InitializationValidator initializationValidator;
    private final String deeplinkPrefix;
    private final int cacheExpireSecond;

    public InitializationCommonPaymentService(CacheService cacheService, PaymentService paymentService, InitializationValidator initializationValidator, @Value("${common.payment.initial.deeplink.url}") String deeplinkPrefix, @Value("${common.payment.initial.cache.expire.second}") int cacheExpireSecond) {
        this.cacheService = cacheService;
        this.paymentService = paymentService;
        this.initializationValidator = initializationValidator;
        this.deeplinkPrefix = deeplinkPrefix;
        this.cacheExpireSecond = cacheExpireSecond;
    }


    public InitializationCommonPaymentResponse initialCommonPayment(InitializationCommonPaymentRequest initializationCommonPaymentRequest, HttpHeaders headers) throws TMBCommonException {
        final String transactionId = UUID.randomUUID().toString();
        final String key = COMMON_PAYMENT_CACHE_PREFIX + transactionId;
        final String compCode = initializationCommonPaymentRequest.getPaymentInformation().getCompCode();
        final String partnerName = headers.getFirst(CommonPaymentExpConstant.HEADER_X_PARTNER_NAME);

        InitialPrepareData prepareData = prepareData(initializationCommonPaymentRequest, headers);

        validateData(initializationCommonPaymentRequest, headers, prepareData);

        final String processorType = getProcessorType(compCode, initializationCommonPaymentRequest, prepareData);

        setCache(key, CommonPaymentMapper.INSTANCE.toCommonPaymentDraftCache(initializationCommonPaymentRequest.getPaymentInformation(), processorType, prepareData, partnerName));

        String deeplinkUrlWithQueryParam = UriComponentsBuilder.fromUriString(deeplinkPrefix)
                .queryParam("transaction_id", transactionId)
                .toUriString();
        return new InitializationCommonPaymentResponse().setDeeplinkUrl(deeplinkUrlWithQueryParam);
    }

    private void validateData(InitializationCommonPaymentRequest initializationCommonPaymentRequest, HttpHeaders headers, InitialPrepareData prepareData) throws TMBCommonException {
        initializationValidator.validateData(initializationCommonPaymentRequest, headers, prepareData);
    }

    private InitialPrepareData prepareData(InitializationCommonPaymentRequest initializationCommonPaymentRequest, HttpHeaders headers) throws TMBCommonException {
        final String entryId = initializationCommonPaymentRequest.getPaymentInformation().getEntryId();
        final String transactionType = initializationCommonPaymentRequest.getPaymentInformation().getTransactionType();
        final String correlationId = headers.getFirst(CommonPaymentExpConstant.HEADER_CORRELATION_ID);
        final String osVersion = headers.getFirst(CommonPaymentExpConstant.HEADER_OS_VERSION);
        final String compCode = initializationCommonPaymentRequest.getPaymentInformation().getCompCode();
        final String clientVersion = headers.getFirst(CommonPaymentExpConstant.HEADER_CLIENT_VERSION);
        final boolean isTransactionFromDeeplink = StringUtils.isNotBlank(initializationCommonPaymentRequest.getPaymentInformation().getDeepLinkTransactionId());

        MasterBillerResponse masterBiller = null;
        DeepLinkRequest deepLinkRequest = null;

        if (isTransactionTypeBillPay(transactionType)) {
            masterBiller = paymentService.getMasterBillerOrElseThrow(correlationId, compCode, osVersion, clientVersion,
                    () -> {
                        logger.error("Error when fetch Master biller not found. Please verify CompCode. [ compCode = {}]", compCode);
                        return CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.MASTER_BILLER_NOT_FOUND_ERROR);
                    });
        }

        final CommonPaymentConfig commonPaymentConfig = paymentService.getCommonPaymentConfig(correlationId, entryId);

        if (isTransactionFromDeeplink) {
            deepLinkRequest = retrieveDeeplinkRequestFromCache(initializationCommonPaymentRequest);
        }

        return new InitialPrepareData()
                .setMasterBillerResponse(masterBiller)
                .setCommonPaymentConfig(commonPaymentConfig)
                .setDeepLinkRequest(deepLinkRequest);
    }

    private void setCache(String key, CommonPaymentDraftCache commonPaymentDraftCache) throws TMBCommonException {
        try {
            cacheService.set(key, COMMON_PAYMENT_HASH_KEY_CACHE, commonPaymentDraftCache, cacheExpireSecond);
        } catch (JsonProcessingException e) {
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Can not parse request model to object");
        }
    }


    private DeepLinkRequest retrieveDeeplinkRequestFromCache(InitializationCommonPaymentRequest initializationCommonPaymentRequest) throws TMBCommonException {
        return (DeepLinkRequest) cacheService.get(initializationCommonPaymentRequest.getPaymentInformation().getDeepLinkTransactionId(), DeepLinkRequest.class);
    }

    private String getProcessorType(String compCode, InitializationCommonPaymentRequest request, InitialPrepareData prepareData) throws TMBCommonException {
        final String transactionType = request.getPaymentInformation().getTransactionType();
        final boolean isScheduleTransaction = request.getPaymentInformation().getSchedule() != null;
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();

        if (isScheduleTransaction) {
            return PROCESSOR_SCHEDULE_UNIMPLEMENTED;
        }

        if (isTransactionTypeBillPay(transactionType)) {
            return ProcessorSelectorUtils.getBillProcessorTypeOverride(compCode, masterBillerResponse);
        }

        return transactionType;

    }
}
