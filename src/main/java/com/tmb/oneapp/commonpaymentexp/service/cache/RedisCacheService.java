package com.tmb.oneapp.commonpaymentexp.service.cache;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

@Service
public class RedisCacheService implements CacheService {
    private static final TMBLogger<RedisCacheService> logger = new TMBLogger<>(RedisCacheService.class);
    private static final String START_SET_CACHE_MESSAGE_TEMPLATE = "Start Set key: {} , value: {} to Redis";
    private static final String SUCCESS_SET_CACHE_MESSAGE_TEMPLATE = "Success Set key: {} , value: {} to Redis";

    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectWithTimeMapper;

    public RedisCacheService(RedisTemplate<String, String> redisTemplate, @Qualifier("objectWithTimeMapper") ObjectMapper objectWithTimeMapper) {
        this.redisTemplate = redisTemplate;
        this.objectWithTimeMapper = objectWithTimeMapper;
    }

    @Override
    public void set(String key, Object value, int ttlSecond) throws JsonProcessingException {
        try {
            logger.debug(START_SET_CACHE_MESSAGE_TEMPLATE, key, value);
            redisTemplate.opsForValue().set(key, TMBUtils.convertJavaObjectToStringWithTimeModule(value), Duration.ofSeconds(ttlSecond));
            logger.debug(SUCCESS_SET_CACHE_MESSAGE_TEMPLATE, key, value);
        } catch (Exception e) {
            logger.warn("exception while setting data to Redis: ", e);
            throw e;
        }
    }

    @Override
    public void set(String key, Object value) throws JsonProcessingException {
        try {
            logger.debug(START_SET_CACHE_MESSAGE_TEMPLATE, key, value);
            redisTemplate.opsForValue().set(key, TMBUtils.convertJavaObjectToStringWithTimeModule(value));
            logger.debug(SUCCESS_SET_CACHE_MESSAGE_TEMPLATE, key, value);
        } catch (Exception e) {
            logger.warn("exception while setting data to Redis: ", e);
            throw e;
        }
    }

    @Override
    public void set(String key, String hashKey, Object value, int ttlSecond) throws JsonProcessingException {
        try {
            String jsonValue = TMBUtils.convertJavaObjectToStringWithTimeModule(value);
            redisTemplate.opsForHash().put(key, hashKey, jsonValue);
            redisTemplate.expire(key, Duration.ofSeconds(ttlSecond));
            logger.debug("Set hash key: {}, hash field: {}, value: {} in Redis", key, hashKey, jsonValue);
        } catch (Exception e) {
            logger.warn("Exception while setting hash key in Redis: ", e);
            throw e;
        }
    }

    @Override
    public void set(String key, String hashKey, Object value) throws JsonProcessingException {
        try {
            String jsonValue = TMBUtils.convertJavaObjectToStringWithTimeModule(value);
            redisTemplate.opsForHash().put(key, hashKey, jsonValue);
            logger.debug("Set hash key: {}, hash field: {}, value: {} in Redis", key, hashKey, jsonValue);
        } catch (Exception e) {
            logger.warn("Exception while setting hash key in Redis: ", e);
            throw e;
        }
    }

    @Override
    public String get(String key) throws TMBCommonException {
        String data = null;
        try {
            data = redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            logger.error("Exception while fetching data from Redis: ", e);
        }
        return data;
    }

    @Override
    public <T> Object get(String key, Class<T> typeValue) throws TMBCommonException {
        try {
            String rawData = redisTemplate.opsForValue().get(key);
            return objectWithTimeMapper.readValue(rawData, typeValue);
        } catch (JsonProcessingException e) {
            logger.warn("cannot parsing json from Redis: ", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            logger.error("exception while fetching data from Redis: ", e);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_NOT_FOUND_ERROR);
        }
    }

    @Override
    public <T> Object get(String key, String hashKey, Class<T> typeValue) throws TMBCommonException {
        try {
            String rawData = (String) redisTemplate.opsForHash().get(key, hashKey);
            if (rawData != null) {
                return objectWithTimeMapper.readValue(rawData, typeValue);
            }
            return null;
        } catch (JsonProcessingException e) {
            logger.warn("Cannot parse JSON from Redis: ", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            logger.error("Exception while fetching hash key from Redis: ", e);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_NOT_FOUND_ERROR);
        }
    }

    @Override
    public boolean delete(String key) {
        try {
            logger.debug("Start delete key: {}", key);
            return Boolean.TRUE.equals(redisTemplate.delete(key));
        } catch (Exception e) {
            logger.warn("exception while deleting to Redis: ", e);
            throw e;
        }
    }

    @Override
    public boolean delete(String key, String hashKey) {
        try {
            Long resultRemoved = redisTemplate.opsForHash().delete(key, hashKey);
            return resultRemoved > 0;
        } catch (Exception e) {
            logger.warn("Exception while deleting hash key from Redis: ", e);
            throw e;
        }
    }

    @Override
    public boolean putIfAbsent(String key, String hashKey, Object value) {
        try {
            return redisTemplate.opsForHash().putIfAbsent(key, hashKey, value);
        } catch (Exception e) {
            logger.warn("Exception while put hash key if absent from Redis: ", e);
            throw e;
        }
    }
}
