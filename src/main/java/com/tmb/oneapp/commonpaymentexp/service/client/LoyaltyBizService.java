package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.client.LoyaltyBizClient;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LoyaltyBizService {
    private static final TMBLogger<LoyaltyBizService> logger = new TMBLogger<>(LoyaltyBizService.class);

    private final LoyaltyBizClient loyaltyBizClient;
    private final FeignClientHelper feignClientHelper;

    public void redeemPoint(HttpHeaders headers, WowPointRedeemConfirmRequest redeemRequest) throws TMBCommonException {
        logger.info(">>> call post loyalty-biz/redeem-points {}<<<", redeemRequest);
        try {
            HttpHeaders redeemHeaders = new HttpHeaders();
            redeemHeaders.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, headers.getFirst(CommonPaymentExpConstant.HEADER_CORRELATION_ID));
            redeemHeaders.add(CommonPaymentExpConstant.CONTENT_TYPE, CommonPaymentExpConstant.APPLICATION_JSON);
            feignClientHelper.executeVoidRequest(() -> loyaltyBizClient.redeemPoint(redeemHeaders, redeemRequest));
        } catch (TMBCommonException e) {
            throw new TMBCommonException(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), e.getErrorMessage(), ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null);
        }
    }
}