package com.tmb.oneapp.commonpaymentexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.common.util.VersionUtils;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.client.PaymentServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentRule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.Address;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CommonPaymentMethodPrepareDataTemp;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CreditCardPoint;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.PaymentMethodCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.ProcessBillPayDataTemp;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.ProcessCommonPaymentMethodDataTemp;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerDetailResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerSearchRequest;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.DStatementAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.IssueDebitCardAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.*;

@Service
@RequiredArgsConstructor
public class PaymentMethodCommonPaymentService {
    private static final TMBLogger<PaymentMethodCommonPaymentService> logger = new TMBLogger<>(PaymentMethodCommonPaymentService.class);
    private final AsyncHelper asyncHelper;
    private final CacheService cacheService;
    private final PaymentServiceClient paymentServiceClient;
    private final CustomerServiceClient customerServiceClient;
    private final DStatementAccountCommonPaymentService dStatementAccountCommonPaymentService;
    private final IssueDebitCardAccountCommonPaymentService issueDebitCardAccountCommonPaymentService;
    private final BillPayPaymentMethodCommonPaymentHelper billPayPaymentMethodCommonPaymentHelper;

    public static final String DEFAULT_PAYMENT_DEPOSIT = "deposit";
    public static final String DEFAULT_PAYMENT_CREDIT_CARD = "credit_card";
    public static final String COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY = "bill_pay";
    public static final String COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT = "d_statement";
    public static final String COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD = "issue_debit_card";
    public static final String COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP = "top_up";
    public static final String COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY = "bill_pay";
    public static final String COMMON_PAYMENT_TRANSACTION_SUBTYPE_D_STATEMENT = "d_statement";
    public static final String COMMON_PAYMENT_TRANSACTION_SUBTYPE_ISSUE_DEBIT_CARD = "issue_debit_card";
    public static final String COMMON_PAYMENT_CONTACT_ADDRESS_TYPE = "contact_address";
    public static final String COMMON_PAYMENT_REGISTER_ADDRESS_TYPE = "register_address";

    public PaymentMethodCommonPaymentResponse getCommonPaymentMethod(String transactionId, HttpHeaders headers) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String clientVersion = headers.getFirst(HEADER_CLIENT_VERSION);
        PaymentMethodCommonPaymentResponse response;

        final CommonPaymentDraftCache cache = getCache(transactionId);

        ValidateClientVersionIfCallFromPartner(cache, clientVersion);

        CommonPaymentMethodPrepareDataTemp prepareData = prepareData(cache, correlationId, crmId);

        ProcessCommonPaymentMethodDataTemp processData = processCommonPaymentMethod(cache, prepareData, headers);

        saveWowPointConfigurationToCache(cache, prepareData, transactionId);

        response = mapToPaymentMethodCommonPaymentResponse(prepareData, processData, cache);

        return response;
    }

    private void ValidateClientVersionIfCallFromPartner(CommonPaymentDraftCache cache, String clientVersion) throws TMBCommonException {
        final String minimumVersion = "5.14.0";
        boolean isClientVersionLessThanMinimumRequirementForPartnerAppToApp = VersionUtils.compare(clientVersion, minimumVersion) < 0;
        boolean isCallFromPartner = StringUtils.isNotEmpty(cache.getPartnerName());
        if (isCallFromPartner && isClientVersionLessThanMinimumRequirementForPartnerAppToApp) {
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.PB_UPDATE_APP.getCode(), "Client version is less than minimum requirement for partner app to app");
        }
    }

    private void saveWowPointConfigurationToCache(CommonPaymentDraftCache cache, CommonPaymentMethodPrepareDataTemp prepareData, String transactionId) throws TMBCommonException {
        if (ObjectUtils.anyNull(cache, prepareData, transactionId)) {
            return;
        }

        var commonPaymentRule = prepareData.getCommonPaymentRule();
        if (commonPaymentRule == null || !commonPaymentRule.isWowPointFlag()) {
            return;
        }

        try {
            CommonPaymentRuleInCache commonPaymentRuleInCache = new CommonPaymentRuleInCache();
            commonPaymentRuleInCache.setWowPointFlag(true);
            commonPaymentRuleInCache.setWowPoint(commonPaymentRule.getWowPoint());
            cache.setCommonPaymentRule(commonPaymentRuleInCache);

            String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + transactionId;
            cacheService.set(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, cache);
        } catch (JsonProcessingException e) {
            throw new TMBCommonException("Failed to add WowPoint data to cache for transaction: " + transactionId, e);
        }
    }

    private PaymentMethodCommonPaymentResponse mapToPaymentMethodCommonPaymentResponse(CommonPaymentMethodPrepareDataTemp prepareData, ProcessCommonPaymentMethodDataTemp processData, CommonPaymentDraftCache cache) {
        final var commonPaymentRule = prepareData.getCommonPaymentRule();
        final var customerDetailResponse = prepareData.getCustomerDetailResponseList().get(0);
        final var masterBillerResponse = prepareData.getMasterBillerResponse();

        PaymentMethodCommonPaymentResponse response = new PaymentMethodCommonPaymentResponse();
        response.setDepositAccountFlag(true);
        switch (StringUtils.trimToEmpty(cache.getPaymentInformation().getTransactionType()).toLowerCase()) {
            case COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY:
                setUpBillPayResponsePassReference(processData, response, masterBillerResponse);
                break;
            case COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD, COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT:
            default:
                if (processData.getDepositAccountList() != null) {
                    response.setDepositAccountList(CacheMapper.INSTANCE.toListDepositAccountInCache(processData.getDepositAccountList()));
                }
                break;
        }

        mappingFlagFromCommonPaymentRule(response, commonPaymentRule, cache);

        if (commonPaymentRule.isCreditCardPointFlag()) {
            response.setCreditCardPointFlag(true);
            response.setCreditCardPoint(mapToCreditCardPoint(processData));
        }

        if (cache.getPaymentInformation().isRequireAddressFlag()) {
            response.setAddress(mapToAddress(customerDetailResponse));
        }

        if (StringUtils.isNotEmpty(cache.getPartnerName())) {
            response.setPartnerFlag(true);
        }

        String defaultPaymentOverride = chooseDefaultPayment(processData);

        response.setDefaultPaymentOverride(defaultPaymentOverride)
                .setPaymentInformation(cache.getPaymentInformation())
                .setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse));
        return response;
    }

    private Address mapToAddress(CustomerDetailResponse customerDetailResponse) {
        String addressType;
        String contactAddress;
        boolean isContactAddressType = customerDetailResponse.getContactAddress() != null;
        if (isContactAddressType) {
            addressType = COMMON_PAYMENT_CONTACT_ADDRESS_TYPE;
            contactAddress = customerDetailResponse.getContactAddress();
        } else {
            addressType = COMMON_PAYMENT_REGISTER_ADDRESS_TYPE;
            contactAddress = customerDetailResponse.getRegisterAddress();

        }

        return new Address()
                .setAddressType(addressType)
                .setContactAddress(contactAddress)
                .setCustomerNameEn(customerDetailResponse.getCustomerNameEn())
                .setCustomerNameTh(customerDetailResponse.getCustomerNameTh());
    }

    private void setUpBillPayResponsePassReference(ProcessCommonPaymentMethodDataTemp processData, PaymentMethodCommonPaymentResponse response, MasterBillerResponse masterBillerResponse) {
        boolean isAllowPayWithCreditCard = masterBillerResponse.getBillerInfo().isCreditCardFlag();
        if (isAllowPayWithCreditCard) {
            response.setCreditCardFlag(true);
            if (processData.getCreditCardList() != null) {
                response.setCreditCardList(CacheMapper.INSTANCE.toListCreditCardSupplementaryInCache(processData.getCreditCardList()));
            }
        }
        if (processData.getDepositAccountList() != null) {
            response.setDepositAccountList(CacheMapper.INSTANCE.toListDepositAccountInCache(processData.getDepositAccountList()));
        }
    }

    private String chooseDefaultPayment(ProcessCommonPaymentMethodDataTemp processData) {
        if (processData.getDepositAccountList() != null) {
            return DEFAULT_PAYMENT_DEPOSIT;
        } else {
            if (processData.getCreditCardList() != null) {
                return DEFAULT_PAYMENT_CREDIT_CARD;
            }
        }
        return null;
    }

    private void mappingFlagFromCommonPaymentRule(PaymentMethodCommonPaymentResponse response, CommonPaymentRule commonPaymentRule, CommonPaymentDraftCache cache) {
        final boolean isScheduleTransaction = cache.getPaymentInformation().getSchedule() != null;

        response.setCreditCardOtherFlag(commonPaymentRule.isCreditCardOtherFlag());
        response.setQrCodeFlag(commonPaymentRule.isQrCodeFlag());
        response.setCouponFlag(commonPaymentRule.isCouponFlag());
        response.setCreditCardInstallmentFlag(commonPaymentRule.isCreditCardInstallmentsFlag());

        if (!isScheduleTransaction) {
            response.setWowPointFlag(commonPaymentRule.isWowPointFlag());
            response.setWowPoint(commonPaymentRule.getWowPoint());
        }
    }

    private List<CreditCardPoint> mapToCreditCardPoint(ProcessCommonPaymentMethodDataTemp processData) {
        if (processData == null || CollectionUtils.isEmpty(processData.getCreditCardList())) {
            return null;
        }

        return processData.getCreditCardList().stream().map(c -> {
            CreditCardPoint cp = new CreditCardPoint();
            cp.setCardNo(c.getCardNo());
            cp.setAccountId(c.getAccountId());
            cp.setProductNameEn(c.getProductNameEn());
            cp.setProductNameTh(c.getProductNameTh());
            cp.setPointEarned(c.getCardPoints().getPointEarned());
            cp.setPointUsed(c.getCardPoints().getPointUsed());
            cp.setPointAvailable(c.getCardPoints().getPointAvailable());
            cp.setPointRemain(c.getCardPoints().getPointRemain());
            cp.setExpiryDate(c.getCardPoints().getExpiryDate());
            cp.setExpiryPoints(c.getCardPoints().getExpiryPoints());
            return cp;
        }).toList();
    }

    private ProcessCommonPaymentMethodDataTemp processCommonPaymentMethod(CommonPaymentDraftCache cache, CommonPaymentMethodPrepareDataTemp prepareData, HttpHeaders headers) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final CommonPaymentRule commonPaymentRule = prepareData.getCommonPaymentRule();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();

        ProcessCommonPaymentMethodDataTemp response = new ProcessCommonPaymentMethodDataTemp();
        switch (StringUtils.trimToEmpty(cache.getPaymentInformation().getTransactionType()).toLowerCase()) {
            case COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY -> {
                ProcessBillPayDataTemp processBillPayDataTemp = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);
                response.setDepositAccountList(processBillPayDataTemp.getDepositAccountList())
                        .setCreditCardList(processBillPayDataTemp.getCreditCardList());
            }
            case COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT -> {
                try {
                    List<DepositAccount> dStatementDepostAccountList = dStatementAccountCommonPaymentService.getAccountList(correlationId, crmId);
                    response.setDepositAccountList(dStatementDepostAccountList);
                } catch (TMBCommonException ignore) {
                    //ignore exception
                }
            }
            case COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD -> {
                try {
                    List<DepositAccount> issueDebitCardDepostAccountList = issueDebitCardAccountCommonPaymentService.getAccountList(correlationId, crmId);
                    response.setDepositAccountList(issueDebitCardDepostAccountList);
                } catch (TMBCommonException ignore) {
                    //ignore exception
                }
            }
            default -> {
                logger.error("Request type not match with type, Please verify type in cache. [ type = {}]", cache.getPaymentInformation().getTransactionType());
                throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.FAILED_V2, "Request type not match. Please verify request type from Initial process");
            }
        }
        return response;
    }

    private CommonPaymentMethodPrepareDataTemp prepareData(CommonPaymentDraftCache cache, String correlationId, String crmId) throws TMBCommonException {
        CompletableFuture<CommonPaymentRule> commonPaymentRuleFuture = getCommonPaymentRuleAsync(cache, correlationId);

        CompletableFuture<MasterBillerResponse> billerDetailFuture = getMasterBillerAsync(correlationId, cache);

        CompletableFuture<List<CustomerDetailResponse>> customerDetailResponse = getCustomerDetailAsync(correlationId, crmId);

        CompletableFuture.allOf(commonPaymentRuleFuture, billerDetailFuture, customerDetailResponse);

        try {
            return new CommonPaymentMethodPrepareDataTemp()
                    .setCommonPaymentRule(commonPaymentRuleFuture.get())
                    .setMasterBillerResponse(billerDetailFuture.get())
                    .setCustomerDetailResponseList(customerDetailResponse.get());

        } catch (ExecutionException | InterruptedException | ThreadDeath e) {
            if (e.getCause() instanceof TMBCommonException ex) {
                logger.error("Error ExecutionException TMBCommonException in method PrepareData : {}", ex.getMessage(), e);
                throw ex;
            }
            Thread.currentThread().interrupt();
            logger.error("Error ExecutionException in method PrepareData", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Error execution exception get prepare data. Please verify prepareData method");
        }
    }

    private CompletableFuture<List<CustomerDetailResponse>> getCustomerDetailAsync(String correlationId, String crmId) throws TMBCommonException {
        var request = new CustomerSearchRequest().setSearchValue(crmId).setSearchType("rm-id");
        return asyncHelper.executeRequestAsync(() -> customerServiceClient.fetchCustomerSearchRealtime(correlationId, request));
    }

    private CompletableFuture<MasterBillerResponse> getMasterBillerAsync(String correlationId, CommonPaymentDraftCache cache) throws TMBCommonException {
        return asyncHelper.executeRequestAsync(() -> paymentServiceClient.fetchMasterBillerByCompCode(correlationId, cache.getPaymentInformation().getCompCode()));
    }

    private CompletableFuture<CommonPaymentRule> getCommonPaymentRuleAsync(CommonPaymentDraftCache cache, String correlationId) throws TMBCommonException {
        boolean isNotBillPayTransaction = StringUtils.isBlank(cache.getPaymentInformation().getCompCode());
        if (isNotBillPayTransaction) {
            return asyncHelper.executeRequestAsync(() -> paymentServiceClient.fetchCommonPaymentRuleByType(correlationId, cache.getPaymentInformation().getTransactionType()));
        }

        return asyncHelper.executeRequestAsync(() -> paymentServiceClient.fetchCommonPaymentRuleByTypeAndCode(correlationId, cache.getPaymentInformation().getTransactionType(), cache.getPaymentInformation().getCompCode()));
    }

    private CommonPaymentDraftCache getCache(String transactionId) throws TMBCommonException {
        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + transactionId;
        return (CommonPaymentDraftCache) Optional.ofNullable(cacheService.get(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, CommonPaymentDraftCache.class))
                .orElseThrow(() -> CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_NOT_FOUND_ERROR));
    }
}
