package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityLogConfirmMapper;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.BaseActivityConfirmEvent;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.LogsConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseConfirmLogRecord;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialConfirmLog;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialLogMapper;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationMapper;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.BaseTransactionConfirmLog;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionLogMapper;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.DateUtils;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialLogMapper.ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID;

@Service
@RequiredArgsConstructor
public class LoanConfirmServiceProcessor extends ConfirmationProcessingTemplate<OCPBillPayment,
        BasePrepareDataConfirm,
        BaseConfirmDataAfterConfirmExternal,
        LogsConfirm> {

    private static final TMBLogger<LoanConfirmServiceProcessor> logger = new TMBLogger<>(LoanConfirmServiceProcessor.class);
    private final DailyLimitService dailyLimitService;
    private final PaymentService paymentService;
    private final CommonValidateConfirmationService commonValidateConfirmationService;
    private final CustomersTransactionService customersTransactionService;
    private final LogEventPublisherService logEventPublisherService;
    private final AsyncHelper asyncHelper;
    private final NotificationCommonPaymentService notificationCommonPaymentService;
    private final BaseConfirmServiceHelper baseConfirmServiceHelper;
    private final LoyaltyBizService loyaltyBizService;

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_TMB_LOAN;
    }

    @Override
    protected LogsConfirm initialLogs(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionTime = String.valueOf(System.currentTimeMillis());
        final String refId = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getEpayCode();
        final BaseConfirmLogRecord baseConfirmLogRecord = new BaseConfirmLogRecord(refId, crmId, correlationId, transactionTime, ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID);

        var activityLogLoan = ActivityLogConfirmMapper.INSTANCE.mapToActivityLoanBillPayConfirmationEvent(headers, draftCache);
        var financialLogLoan = FinancialLogMapper.INSTANCE.mapToFinancialLoanBillPayActivityLog(baseConfirmLogRecord, activityLogLoan.getActivityTypeId(), draftCache);
        var transactionLogLoan = TransactionLogMapper.INSTANCE.mapToTransactionActivityLoanBillPay(baseConfirmLogRecord, draftCache);

        return new LogsConfirm()
                .setActivityConfirmEvent(activityLogLoan)
                .setFinancialActivityLog(financialLogLoan)
                .setTransactionActivityLog(transactionLogLoan)
                .setActivityCustomSlipCompleteEvent(new ActivityCustomSlipCompleteEvent(headers, request.getCustomSlip(), CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL));
    }

    @Override
    protected BasePrepareDataConfirm prepareData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, LogsConfirm logEvents) throws TMBCommonException {
        return baseConfirmServiceHelper.getBasePrepareDataConfirm(headers, logEvents.getFinancialActivityLog().getTxnDt());
    }

    @Override
    protected BasePrepareDataConfirm validateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException {
        final String transactionId = request.getTransactionId();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final boolean isPayByOwner = NullSafeUtils.getSafeNullOrDefault(() -> draftCache.getValidateDraftCache().getAdditionalParam().isPayByOwner(), false);

        this.validateServiceHours(draftCache);
        this.validateAuthentication(headers, draftCache, transactionId);
        this.validateDuplicateTransaction(transactionId);

        if (!isPayByOwner) {
            this.validateDailyLimitExceeded(draftCache, customerCrmProfile);
        }

        return prepareData;
    }

    private void validateDuplicateTransaction(String transactionId) throws TMBCommonException {
        commonValidateConfirmationService.validateTransactionByTransactionId(transactionId);
    }

    private void validateAuthentication(HttpHeaders headers, CommonPaymentDraftCache draftCache, String transactionId) throws TMBCommonException {
        commonValidateConfirmationService.verifyAuthentication(transactionId, draftCache, headers);
    }

    private void validateServiceHours(CommonPaymentDraftCache draftCache) throws TMBCommonException {
        commonValidateConfirmationService.validateServiceHours(draftCache);
    }

    private void validateDailyLimitExceeded(CommonPaymentDraftCache draftCache, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        final String amountFromConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getAmount();

        dailyLimitService.validateDailyLimitExceeded(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType(), customerCrmProfile, new BigDecimal(amountFromConfirmRequest));
    }

    @Override
    protected OCPBillPayment confirmWithExternalService(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();
        OCPBillPaymentResponse ocpBillPaymentEntity;
        OCPBillRequest ocpRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();

        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            WowPointRedeemConfirmRequest redeemRequest = draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();
            loyaltyBizService.redeemPoint(headers, redeemRequest);

            ocpBillPaymentEntity = paymentService.confirmOCPBillWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpRequest);
        } else {
            ocpBillPaymentEntity = paymentService.confirmOCPBillPayment(correlationId, crmId, transactionId, ocpRequest);
        }

        return ocpBillPaymentEntity.getData();
    }


    @Override
    protected BaseConfirmDataAfterConfirmExternal processAfterConfirmWithExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();
        final String transactionTime = prepareData.getTransactionTime();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final boolean isPayByOwner = draftCache.getValidateDraftCache().getAdditionalParam().isPayByOwner();
        final boolean isRequireCommonAuth = draftCache.getValidateDraftCache().isRequireCommonAuthen();
        final String ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getEpayCode();
        final String amountFromConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getAmount();

        baseConfirmServiceHelper.baseClearDraftDataCache(transactionId);
        this.clearDepositCache(correlationId, crmId);
        baseConfirmServiceHelper.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, isRequireCommonAuth);
        baseConfirmServiceHelper.baseExecuteCallbackIfConfiguredAsync(draftCache, transactionTime, amountFromConfirmRequest);

        if (!isPayByOwner) {
            dailyLimitService.updateAccumulateUsage(draftCache, customerCrmProfile, crmId, correlationId);
        }

        NotificationCommonPayment notificationRequest = NotificationMapper.INSTANCE.toCommonPaymentNotification(draftCache, ePayCode, crmId, correlationId, transactionTime);
        asyncHelper.executeMethodAsyncSafelyVoid(() -> notificationCommonPaymentService.sendENotification(notificationRequest));

        return null;
    }

    private void clearDepositCache(String correlationId, String crmId) {
        customersTransactionService.clearDepositCache(correlationId, crmId);
    }

    @Override
    protected void saveSuccessLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse, LogsConfirm logEvents, BaseConfirmDataAfterConfirmExternal processAfterConfirmResult) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final BaseActivityConfirmEvent activityLog = logEvents.getActivityConfirmEvent();
        final ActivityCustomSlipCompleteEvent customSlipLog = logEvents.getActivityCustomSlipCompleteEvent();
        final var transactionActivityLog = logEvents.getTransactionActivityLog();

        var financialLog = logEvents.getFinancialActivityLog();
        financialLog.setBillerCustomerName(externalResponse.getAccount().getTitle());
        financialLog.setTxnBal(externalResponse.getAccount().getLedgerBal());

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog, customSlipLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
    }

    @Override
    protected ConfirmationCommonPaymentResponse mappingResponse(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse) {
        final String ePayCode = externalResponse.getEpayCode();
        final String transactionTime = prepareData.getTransactionTime();
        final CustomerCrmProfile customerProfile = prepareData.getCustomerCrmProfile();

        ConfirmationCommonPaymentResponse response = new ConfirmationCommonPaymentResponse();
        response.setReferenceNo(ePayCode);
        response.setTransactionCreatedDatetime(DateUtils.formatTimestampToISO(transactionTime));
        response.setRemainingBalance(Optional.ofNullable(externalResponse.getAccount()).map(OCPAccount::getAvailBal).map(BigDecimal::new).orElse(null));
        response.setCompleteScreenDetail(draftCache.getPaymentInformation().getCompleteScreenDetail());
        response.setAutoSaveSlip(StringUtils.equalsAnyIgnoreCase("Y", customerProfile.getAutoSaveSlipMain()));

        return response;
    }

    @Override
    protected TMBCommonException handleException(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, LogsConfirm logEvents, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process LoanConfirmation. : {}", e.getMessage());
        throw baseConfirmServiceHelper.baseHandleException(request, e);
    }

    @Override
    protected void saveFailedLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, LogsConfirm logEvents, Exception e) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        BaseActivityConfirmEvent activityLog = logEvents.getActivityConfirmEvent();
        BaseFinancialConfirmLog financialLog = logEvents.getFinancialActivityLog();
        BaseTransactionConfirmLog transactionActivityLog = logEvents.getTransactionActivityLog();

        activityLog.setFailureStatusWithReasonFromException(e);
        financialLog.setFailureStatusWithErrorCodeFromException(e);
        transactionActivityLog.setTransactionStatus(ACTIVITY_FAILURE);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
    }
}