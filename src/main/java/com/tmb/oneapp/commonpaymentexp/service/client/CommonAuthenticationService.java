package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CommonAuthenticationClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthForceFR;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRefResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRule;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.Supplier;

@Service
@RequiredArgsConstructor
public class CommonAuthenticationService {
    private static final TMBLogger<CommonAuthenticationService> logger = new TMBLogger<>(CommonAuthenticationService.class);
    private final CommonAuthenticationClient commonAuthenticationClient;
    private final FeignClientHelper feignClientHelper;

    public CommonAuthenVerifyRefResponse verifyReferenceId(String correlationId, String crmId, String ipAddress, CommonAuthenVerifyRefRequest request, CommonAuthenVerifyRule rule) throws TMBCommonException {
        logger.info(">>> Call verify common authentication <<<");
        CommonAuthenVerifyRefResponse response = feignClientHelper.executeRequestOrElseThrow(
                () -> commonAuthenticationClient.verifyCommonAuthentication(correlationId, crmId, ipAddress, request),
                () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.AUTHORIZE_FAILED)
        );

        validateRule(rule, response);

        return response;
    }

    public TmbOneServiceResponse<String> getCacheData(String correlationId, String key) {
        logger.info(">>> Call getCacheData <<< correlation {} key {}", correlationId, key);

        ResponseEntity<TmbOneServiceResponse<String>> response = commonAuthenticationClient.getCacheData(correlationId, key);

        return response.getBody();
    }

    @LogAround
    public CommonAuthForceFR getCommonAuthForceFR(String correlationId, String crmId) {
        try {
            return this.executeRequestDataCanBeNull(() -> commonAuthenticationClient.getCommonAuthForceFR(correlationId, crmId));
        } catch (TMBCommonException e) {
            return null;
        }
    }

    private void validateRule(CommonAuthenVerifyRule rule, CommonAuthenVerifyRefResponse response) throws TMBCommonException {
        boolean isSameAmount = toBigDecimal2Digit(response.getAmount()).equals(toBigDecimal2Digit(rule.getAmount()));
        boolean isSameFeatureId = StringUtils.equalsAnyIgnoreCase(response.getFeatureId(), rule.getFeatureId());
        boolean isSameDailyAmount = toBigDecimal2Digit(response.getDailyAmount()).equals(toBigDecimal2Digit(rule.getDailyAmount()));
        boolean isSameBillerCode = StringUtils.isAllBlank(response.getBillerCode(), rule.getCompCode()) || StringUtils.equalsAnyIgnoreCase(response.getBillerCode(), rule.getCompCode());
        boolean isAllSame = isSameAmount && isSameFeatureId && isSameDailyAmount && isSameBillerCode;
        if (!isAllSame) {
            logger.error("Error verifyReferenceId when validate rule : failed. [isSameAmount = {}, isSameFeatureId = {}, isSameDailyAmount = {}, isSameBillerCode = {}]", isSameAmount, isSameFeatureId, isSameDailyAmount, isSameBillerCode);
            String message = ResponseCode.AUTHORIZE_FAILED.getMessage() + ", When request common-authen not matched. Please verify request for call common authen should be match with BE";
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.AUTHORIZE_FAILED.getCode(), message);
        }
    }

    private BigDecimal toBigDecimal2Digit(String input) {
        return new BigDecimal(input).setScale(2, RoundingMode.DOWN);
    }

    private <T> T executeRequestDataCanBeNull(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        try {
            ResponseEntity<TmbServiceResponse<T>> respEntity = supplier.get();

            boolean isNotSuccess = !respEntity.getStatusCode().is2xxSuccessful();
            if (isNotSuccess) {
                throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Non-successful HTTP status code received");
            }

            TmbServiceResponse<T> tmbServiceResponse = respEntity.getBody();

            if (isNotSuccessStatusCode(tmbServiceResponse)) {
                throw CommonServiceUtils.getBusinessTmbCommonException(tmbServiceResponse.getStatus().getCode(), tmbServiceResponse.getStatus().getMessage());
            }

            return tmbServiceResponse.getData();
        } catch (TMBCommonException e) {
            logger.error("FeignException encountered while fetching data from oneapp-auth-service", e);
            throw e;
        } catch (FeignException e) {
            logger.error("FeignException encountered while fetching data from oneapp-auth-service", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "FeignException occurred while fetching data");
        } catch (Exception e) {
            logger.error("Exception encountered while fetching data from oneapp-auth-service", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Exception occurred while fetching data");
        }
    }

    private static <T> boolean isNotSuccessStatusCode(TmbServiceResponse<T> tmbServiceResponse) {
        return tmbServiceResponse == null ||
                !(
                        ResponseCode.SUCCESS.getCode().equals(tmbServiceResponse.getStatus().getCode()) || ResponseCode.SUCCESS_V2.getCode().equals(tmbServiceResponse.getStatus().getCode())
                );
    }
}
