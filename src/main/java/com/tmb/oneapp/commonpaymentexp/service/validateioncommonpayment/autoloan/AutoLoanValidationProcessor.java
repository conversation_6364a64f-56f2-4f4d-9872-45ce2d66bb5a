package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.autoloan;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.tmb.common.cache.Transaction;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayMapper;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AutoLoanOCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.CacheResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanPaymentDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanSender;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanTBankCustomer;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETETransaction;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.AdditionalParamCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan.AutoLoanExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan.AutoLoanPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan.AutoLoanValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.HpExpService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CALL_FROM_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BILLER_TRAN_CODE_AUTO_LOAN_SUFFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BILL_TRAN_CODE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_PRE_LOGIN;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TOP_UP_REF_SEQUENCE_DIGIT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_AUTO_LOAN;

@Service
@RequiredArgsConstructor
public class AutoLoanValidationProcessor extends ValidationProcessingTemplate<
        AutoLoanPrepareDataValidate,
        AutoLoanExternalValidateResponse,
        AutoLoanValidateDataAfterCallExternal,
        ActivityBillPayValidationEvent
        > {

    protected static final TMBLogger<AutoLoanValidationProcessor> logger = new TMBLogger<>(AutoLoanValidationProcessor.class);

    private final PaymentService paymentService;
    private final AccountCommonPaymentHelper accountCommonPaymentHelper;
    private final CustomerService customerService;
    private final BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    private final LogEventPublisherService logEventPublisherService;
    private final CacheService cacheService;
    private final CustomerServiceClient customerServiceClient;
    private final AsyncHelper asyncHelper;
    private final DailyLimitService dailyLimitService;
    private final DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    private final HpExpService hpExpService;
    private final BaseBillPayValidator baseBillPayValidator;

    protected static final String BLANK = "";

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_TMB_AUTO_LOAN;
    }

    @Override
    protected ActivityBillPayValidationEvent initialActivityLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) {
        return ActivityBillPayMapper.INSTANCE.toActivityBillPayValidation(headers, request, cache);
    }

    @Override
    protected AutoLoanPrepareDataValidate prepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();
        AutoLoanPrepareDataValidate prepareData = new AutoLoanPrepareDataValidate();

        try {
            CompletableFuture<BillPayConfiguration> billPayConfigFuture = asyncHelper.executeMethodAsync(() -> paymentService.getBillPayConfig(correlationId));
            CompletableFuture<CustomerKYCResponse> customerKYCResponseFuture = asyncHelper.executeMethodAsync(() -> customerService.getCustomerKYC(correlationId, crmId));
            CompletableFuture<MasterBillerResponse> masterBillerFuture = asyncHelper.executeMethodAsync(() -> paymentService.getMasterBiller(correlationId, compCode));
            CompletableFuture<CustomerCrmProfile> customerCrmProfileFuture = asyncHelper.executeRequestAsync(() -> customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId));
            CompletableFuture<List<LoanAccount>> hpAccountListFuture = asyncHelper.executeMethodAsync(() -> accountCommonPaymentHelper.fetchHpAccountList(correlationId, crmId));
            CompletableFuture<DepositAccount> depositAccountFuture = asyncHelper.executeMethodAsync(() -> billPayAccountCommonPaymentService.getAccountByAccountNumber(request.getDeposit().getAccountNumber(), headers));

            CompletableFuture.allOf(billPayConfigFuture, customerKYCResponseFuture, masterBillerFuture, customerCrmProfileFuture, depositAccountFuture, hpAccountListFuture);

            prepareData.setBillPayConfiguration(billPayConfigFuture.get());
            prepareData.setCustomerKYCResponse(customerKYCResponseFuture.get());
            prepareData.setMasterBillerResponse(masterBillerFuture.get());
            prepareData.setCustomerCrmProfile(customerCrmProfileFuture.get());
            prepareData.setFromDepositAccount(depositAccountFuture.get());
            prepareData.setHpAccountList(hpAccountListFuture.get());

        } catch (ExecutionException | InterruptedException | ThreadDeath e) {
            if (e.getCause() instanceof TMBCommonException ex) {
                logger.error("Error ExecutionException TMBCommonException in method PrepareData : {}", ex.getMessage(), e);
                throw ex;
            }
            Thread.currentThread().interrupt();
            logger.error("Error ExecutionException in method PrepareData", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Error execution exception get prepare data. Please verify prepareData method");
        }

        return prepareData;
    }

    @Override
    protected AutoLoanPrepareDataValidate validateData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, AutoLoanPrepareDataValidate prepareData) throws TMBCommonException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final DeepLinkRequestInCache deepLinkRequest = cache.getDeepLinkRequest();
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final List<LoanAccount> hpAccountList = prepareData.getHpAccountList();
        boolean isPayByOwner = this.isPayBillBySelfTTBAutoLoan(hpAccountList, cache.getPaymentInformation().getProductDetail().getProductRef1());
        final boolean isPayWithWowPoint = !ObjectUtils.isEmpty(cache.getCommonPaymentRule())
                && cache.getCommonPaymentRule().isWowPointFlag() && !ObjectUtils.isEmpty(request.getWowPoint());

        this.validateBillerExpiration(masterBillerResponse);
        this.checkServiceHours(masterBillerResponse);
        this.validateDeepLinkRequest(deepLinkRequest, headers, crmId, cache);

        if (!isPayByOwner) {
            this.validateDailyLimitExceeded(request, masterBillerResponse, customerCrmProfile);
        }

        if (isPayWithWowPoint) {
            baseBillPayValidator.validatePostAuthenticateTransaction(headers.getFirst(HEADER_CORRELATION_ID), request.getTransactionId());
            baseBillPayValidator.validateWowPoint(request, cache);
        }

        return prepareData;
    }

    private void checkServiceHours(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateServiceHours(masterBillerResponse);
    }

    private void validateBillerExpiration(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateBillerExpiration(masterBillerResponse);
    }


    private void validateDailyLimitExceeded(ValidationCommonPaymentRequest request, MasterBillerResponse masterBiller, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        BigDecimal amount = request.getDeposit().getAmount();
        dailyLimitService.validateDailyLimitExceeded(masterBiller.getBillerInfo().getBillerGroupType(), customerCrmProfile, amount);
    }

    private void validateDeepLinkRequest(DeepLinkRequestInCache deepLinkRequest, HttpHeaders headers, String crmId, CommonPaymentDraftCache cache) throws TMBCommonException {
        if (shouldSkipValidation(deepLinkRequest, headers)) {
            return;
        }

        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String acceptLanguage = headers.getFirst(HEADER_ACCEPT_LANGUAGE);
        final String appVersion = headers.getFirst(HEADER_APP_VERSION);

        final String objectiveId = deepLinkRequest.getTransType();

        if (!isTransactionTypeRequiredForAmountCheck(objectiveId)) {
            logger.debug("autoloan checking amount, trans type is NOT required to check: {}", objectiveId);
            return;
        }

        final BigDecimal reqAmount = cache.getPaymentInformation().getAmountDetail().getAmountValue();
        final String ref1 = cache.getPaymentInformation().getProductDetail().getProductRef1();
        final String hpAccount = formatHpAccount(ref1);

        try {
            validateAmountWithCachedData(correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccount, reqAmount);
        } catch (JsonProcessingException e) {
            handleJsonProcessingException(e);
        } catch (TMBCommonException e) {
            handleTMBCommonException(e);
        } catch (Exception e) {
            handleUnexpectedException(e);
        }
    }

    private boolean shouldSkipValidation(DeepLinkRequestInCache deepLinkRequest, HttpHeaders headers) {
        boolean isPreLogin = Boolean.parseBoolean(headers.getFirst(HEADER_PRE_LOGIN));
        return isPreLogin || deepLinkRequest == null || !StringUtils.equalsIgnoreCase(BILLER_CALL_FROM_AUTO_LOAN, deepLinkRequest.getCallFrom());
    }

    private boolean isTransactionTypeRequiredForAmountCheck(String objectiveId) {
        String allTransTypes = "01 02 04";
        return allTransTypes.contains(objectiveId);
    }


    private String formatHpAccount(String ref1) {
        logger.debug("autoloan checking amount ref1: {}", ref1);
        return ref1.replaceAll("(.)(.{2})(.)(.{7})(.{2})$", "$2-$4");
    }

    private void validateAmountWithCachedData(String correlationId, String acceptLanguage, String appVersion,
                                              String crmId, String objectiveId, String hpAccount, BigDecimal reqAmount)
            throws TMBCommonException, JsonProcessingException {
        String cacheKey = crmId + "_" + objectiveId + "_" + hpAccount + "_ALDX_FEE";
        logger.debug("autoloan checking amount getting cache by redis key: {}", cacheKey);

        CacheResponse cacheResponse = hpExpService.getAldxFeeCache(correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccount);

        AutoLoanPaymentDetail paymentDetail = TMBUtils.convertStringToJavaObjWithTypeReference(cacheResponse.getValue().toString(), new TypeReference<>() {
        });

        BigDecimal cachedAmountInBigDecimal = BigDecimal.valueOf(paymentDetail.getDebtFeetotalAmt()).setScale(2, RoundingMode.DOWN);
        BigDecimal requestAmountInBigDecimal = reqAmount.setScale(2, RoundingMode.DOWN);

        if (cachedAmountInBigDecimal.compareTo(requestAmountInBigDecimal) != 0) {
            logger.debug("autoloan checking amount, amount is incorrect ref1: {} req amount:{} cached amount:{}", hpAccount, requestAmountInBigDecimal.toPlainString(), cachedAmountInBigDecimal.toPlainString());
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Payment amount is not valid");
        }
    }


    private void handleJsonProcessingException(JsonProcessingException e) throws TMBCommonException {
        logger.error("autoloan checking amount data error data incorrect: {}", e);
        throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Payment amount is incorrect format");
    }


    private void handleTMBCommonException(TMBCommonException e) throws TMBCommonException {
        logger.error("autoloan checking amount got error: {}", e);
        throw e;
    }

    private void handleUnexpectedException(Exception e) throws TMBCommonException {
        logger.error("autoloan checking amount got unexpected error: {}", e);
        throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2,
                "AutoLoan payment detail unexpected error:" + e.getMessage());
    }

    @Override
    protected AutoLoanExternalValidateResponse validateWithExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, AutoLoanPrepareDataValidate prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final CustomerKYCResponse customerKYC = prepareData.getCustomerKYCResponse();

        AutoLoanExternalValidateResponse externalValidateResponse = new AutoLoanExternalValidateResponse();

        TopUpETEPaymentRequest topUpETEPaymentRequest = AutoLoanRequestMapper.INSTANCE.setupAutoLoanVerifyPaymentRequest(request, cache, customerKYC.getIdNo());

        TopUpETEResponse topUpETEResponse = paymentService.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest);

        validateRef2(cache.getPaymentInformation().getProductDetail().getProductRef2(), topUpETEResponse.getTransaction());

        externalValidateResponse.setTopUpETEPaymentRequest(topUpETEPaymentRequest);
        externalValidateResponse.setTopUpETEResponse(topUpETEResponse);

        return externalValidateResponse;
    }

    protected void validateRef2(String ref2, TopUpETETransaction topUpETETransaction) throws TMBCommonException {
        var customerNumber = Optional.ofNullable(topUpETETransaction)
                .map(TopUpETETransaction::getTbankCustomer)
                .map(AutoLoanTBankCustomer::getCustomerNumber)
                .orElseThrow(() -> CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.AL_REF2_MISSING));
        if (!StringUtils.equals(ref2, customerNumber)) {
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.AL_REF2_MISS_MATCH_ERROR);
        }
    }

    protected String getTransactionId(String key, int digits) {
        return Transaction.getTransactionId(key, digits);
    }

    @Override
    protected AutoLoanValidateDataAfterCallExternal validateAfterCallExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, AutoLoanPrepareDataValidate prepareData, AutoLoanExternalValidateResponse externalResponse) throws TMBCommonException {
        final List<LoanAccount> hpAccountList = prepareData.getHpAccountList();
        final DepositAccount depositAccount = prepareData.getFromDepositAccount();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final TopUpETETransaction topUpETETransaction = externalResponse.getTopUpETEResponse().getTransaction();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();

        AutoLoanValidateDataAfterCallExternal validateDataAfterCallExternal = new AutoLoanValidateDataAfterCallExternal();

        final BigDecimal fee = getFee(topUpETETransaction, depositAccount);
        final BigDecimal amount = request.getAmount();

        this.validateInsufficientFund(request, fromDepositAccount, fee);

        boolean isOwn = this.isPayBillBySelfTTBAutoLoan(hpAccountList, cache.getPaymentInformation().getProductDetail().getProductRef1());

        CommonAuthenResult commonAuthenResult = this.isRequireCommonAuthen(request, headers, customerCrmProfile, isOwn);

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenResponse = null;
        if (commonAuthenResult.isRequireCommonAuthen()) {
            commonAuthenResponse = new CommonAuthenticationValidationCommonPaymentResponse()
                    .setTotalPaymentAccumulateUsage(amount.add(customerCrmProfile.getPaymentAccuUsgAmt()))
                    .setFeatureId(COMMON_PAYMENT_BILL_PAY_FEATURE_ID)
                    .setFlowName(COMMON_AUTH_BILL_FLOW_NAME)
                    .setBillerCompCode(cache.getPaymentInformation().getCompCode());
        }

        BigDecimal totalAmount = amount.add(fee);
        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            totalAmount = totalAmount.subtract(request.getWowPoint().getDiscountAmount());
        }

        validateDataAfterCallExternal.setRequireCommonAuthen(commonAuthenResult.isRequireCommonAuthen());
        validateDataAfterCallExternal.setTotalAmount(totalAmount);
        validateDataAfterCallExternal.setCommonAuthentication(commonAuthenResponse);
        validateDataAfterCallExternal.setCommonAuthenResult(commonAuthenResult);
        validateDataAfterCallExternal.setFeeAfterCalculated(fee);
        validateDataAfterCallExternal.setPayByOwner(isOwn);

        return validateDataAfterCallExternal;
    }

    protected BigDecimal getFee(TopUpETETransaction paymentTransaction, DepositAccount depositAccount) {
        BigDecimal fee = paymentTransaction.getFee().getBillPayment().setScale(2, RoundingMode.HALF_UP);
        if (depositAccount.getWaiveFeeForBillpay().equals("1")) {
            fee = new BigDecimal("0.00");
        }
        return fee;
    }

    protected void validateInsufficientFund(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, BigDecimal fee) throws TMBCommonException {
        baseBillPayValidator.validateInsufficientFund(request, fromDepositAccount, fee);
    }

    private boolean isPayBillBySelfTTBAutoLoan(List<LoanAccount> loanAccounts, String reference1) {

        if (reference1 == null || loanAccounts == null || loanAccounts.isEmpty()) {
            return false;
        }

        String accountNumberFromReference = findAcctNumByRef1(reference1);

        if (accountNumberFromReference.isEmpty()) {
            return false;
        }

        return loanAccounts.stream()
                .map(LoanAccount::getAccountNumber)
                .filter(accountNumber -> accountNumber != null && !accountNumber.isEmpty())
                .anyMatch(accountNumber -> accountNumber.equals(accountNumberFromReference));
    }

    private String findAcctNumByRef1(String ref1) {
        if (ref1.length() < 11) return "";
        return ref1.substring(1, 3) + ref1.substring(4, 11);
    }

    protected CommonAuthenResult isRequireCommonAuthen(ValidationCommonPaymentRequest request, HttpHeaders headers, CustomerCrmProfile customerCrmProfile, boolean isOwn) throws TMBCommonException {
        BigDecimal amount = request.getDeposit().getAmount();
        return dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(headers, amount, isOwn, customerCrmProfile);
    }

    @Override
    protected void saveSuccessLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, AutoLoanPrepareDataValidate prepareData, AutoLoanExternalValidateResponse externalResponse, AutoLoanValidateDataAfterCallExternal autoLoanValidateDataAfterCallExternal, ActivityBillPayValidationEvent activityEvent) {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();

        ActivityBillPayValidationEvent activityEventData = activityEvent;
        activityEventData = ActivityBillPayMapper.INSTANCE.updateActivityAutoLoanBillPayValidation(activityEventData, masterBillerResponse, cache, request, autoLoanValidateDataAfterCallExternal);

        logEventPublisherService.saveActivityLog(activityEventData);
    }

    @Override
    protected void updateCache(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, AutoLoanPrepareDataValidate prepareData, AutoLoanExternalValidateResponse externalResponse, AutoLoanValidateDataAfterCallExternal validateDataAfterCallExternal) throws JsonProcessingException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CommonAuthenticationValidationCommonPaymentResponse commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();

        AutoLoanOCPBillRequest autoLoanOCPBillRequest = null;
        TopUpETEPaymentRequest topUpETEPaymentRequest = null;
        WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest = null;
        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            wowPointRedeemConfirmRequest = WowPointRedeemConfirmRequestMapper.INSTANCE.mapToWowPointRedeemConfirmRequestForOCPAndCustomBill(request, compCode, crmId);
            autoLoanOCPBillRequest = OCPBillRequestMapper.INSTANCE.toAutoLoanOCPBillRequestForConfirmAutoLoanWithWowPayment(request, masterBillerResponse, cache, externalResponse, prepareData, wowPointRedeemConfirmRequest);
        } else {
            topUpETEPaymentRequest = createTopUpETEPaymentConfirmRequest(request, masterBillerResponse, cache, externalResponse, prepareData);
        }

        var validateDraftCache = new ValidationCommonPaymentDraftCache()
                .setExternalConfirmRequest(new ExternalConfirmRequest()
                        .setTopUpETEPaymentRequest(topUpETEPaymentRequest)
                        .setAutoLoanOCPBillPaymentConfirmRequest(autoLoanOCPBillRequest))
                .setRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setFromDepositAccount(CacheMapper.INSTANCE.toDepositAccountInCache(prepareData.getFromDepositAccount()))
                .setMasterBillerResponse(CacheMapper.INSTANCE.toMasterBillerResponseInCache(masterBillerResponse))
                .setCommonAuthentication(commonAuthentication)
                .setFeeCalculated(validateDataAfterCallExternal.getFeeAfterCalculated())
                .setTotalAmount(validateDataAfterCallExternal.getTotalAmount())
                .setWowPointRedeemConfirmRequest(wowPointRedeemConfirmRequest)
                .setAdditionalParam(new AdditionalParamCommonPaymentDraftCache()
                        .setPayByOwner(validateDataAfterCallExternal.isPayByOwner())
                );

        cache.setCrmId(crmId);
        cache.setValidateRequest(request);
        cache.setValidateDraftCache(validateDraftCache);

        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + request.getTransactionId();
        cacheService.set(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, cache);
    }

    private TopUpETEPaymentRequest createTopUpETEPaymentConfirmRequest(ValidationCommonPaymentRequest request,
                                                                       MasterBillerResponse masterBillerResponse,
                                                                       CommonPaymentDraftCache cache,
                                                                       AutoLoanExternalValidateResponse externalResponse,
                                                                       AutoLoanPrepareDataValidate prepareData) {
        TopUpETEPaymentRequest validationRequest = externalResponse.getTopUpETEPaymentRequest();
        TopUpETETransaction transaction = externalResponse.getTopUpETEResponse().getTransaction();
        CustomerKYCResponse customerKYC = prepareData.getCustomerKYCResponse();

        TopUpETEPaymentRequest confirmRequest = new TopUpETEPaymentRequest();
        BeanUtils.copyProperties(validationRequest, confirmRequest);

        confirmRequest.setTellerId(BLANK);
        confirmRequest.setEpayCode(getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT));

        confirmRequest.setFee(transaction.getFee());
        confirmRequest.getFee().setInterregion(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));

        confirmRequest.setFromAccount(createFromAccount(prepareData.getFromDepositAccount()));

        confirmRequest.setToAccount(createToAccount(masterBillerResponse));

        String accountType = confirmRequest.getFromAccount().getAccountType();
        String tranCode = BILL_TRAN_CODE_PREFIX + TMBUtils.generateTransactionCode(accountType) + BILLER_TRAN_CODE_AUTO_LOAN_SUFFIX;
        confirmRequest.setTranCode(tranCode);

        String customerName = transaction.getTbankCustomer().getName();
        confirmRequest.setAutoLoanDetails(createAutoLoanDetails(customerName, confirmRequest.getPaymentId()));
        confirmRequest.setSender(createSender(customerKYC));
        confirmRequest.setAdditionalParams(createAdditionalParams(customerName));

        return confirmRequest;
    }

    private TopUpAccount createFromAccount(DepositAccount depositAccount) {
        TopUpAccount fromAccount = new TopUpAccount();
        fromAccount.setAccountId(depositAccount.getAccountNumber());
        fromAccount.setAccountType(depositAccount.getAccountType());
        return fromAccount;
    }

    private TopUpAccount createToAccount(MasterBillerResponse masterBillerResponse) {
        TopUpAccount toAccount = new TopUpAccount();
        String billerAccountId = masterBillerResponse.getBillerInfo().getToAccountId();
        toAccount.setAccountId(billerAccountId);
        toAccount.setAccountType(TMBUtils.getAccountType(billerAccountId));
        return toAccount;
    }

    private AutoLoanDetail createAutoLoanDetails(String customerName, String paymentId) {
        AutoLoanDetail autoLoanDetail = new AutoLoanDetail();
        autoLoanDetail.setAppId(BLANK);
        autoLoanDetail.setHpMode(BLANK);
        autoLoanDetail.setChannelId("MB");
        autoLoanDetail.setLocation("MB");
        autoLoanDetail.setTerminalId("MB");
        autoLoanDetail.setCustomerName(customerName);

        String traceNo = paymentId.substring(paymentId.length() - 9);
        autoLoanDetail.setTraceNo(traceNo);

        return autoLoanDetail;
    }

    private AutoLoanSender createSender(CustomerKYCResponse customerKYC) {
        AutoLoanSender sender = new AutoLoanSender();
        String fullName = customerKYC.getCustomerFirstNameEn() + " " + customerKYC.getCustomerLastNameEn();
        sender.setFullName(fullName);
        return sender;
    }

    private List<AdditionalParam> createAdditionalParams(String customerName) {
        AdditionalParam customerNameParam = new AdditionalParam();
        customerNameParam.setName("TBankCustomerName");
        customerNameParam.setValue(customerName);

        return Collections.singletonList(customerNameParam);
    }


    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, AutoLoanPrepareDataValidate prepareData, AutoLoanExternalValidateResponse externalResponse, AutoLoanValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final boolean isRequireCommonAuthen = validateDataAfterCallExternal.isRequireCommonAuthen();
        final BigDecimal fee = validateDataAfterCallExternal.getFeeAfterCalculated();
        final CommonAuthenticationValidationCommonPaymentResponse commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final BigDecimal amount = request.getAmount();

        ValidationCommonPaymentResponse response = new ValidationCommonPaymentResponse();
        response.setTransactionId(request.getTransactionId());
        response.setFee(fee);
        response.setAmount(amount);
        response.setTotalAmount(amount.add(fee));
        response.setIsRequireCommonAuthen(isRequireCommonAuthen);

        if (isRequireCommonAuthen) {
            response.setCommonAuthenticationInformation(commonAuthentication);
        }

        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            response.setWowPoint(WowPointValidationCommonPaymentResponseMapper.INSTANCE.mapWowPointValidationCommonPaymentResponse(request));
        }

        return response;
    }

    @Override
    protected TMBCommonException handleException(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, AutoLoanPrepareDataValidate prepareData, AutoLoanExternalValidateResponse externalResponse, AutoLoanValidateDataAfterCallExternal validateDataAfterCallExternal, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process AutoLoanValidation. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException ex) {
            throw ex;
        } else if (e instanceof TMBCommonExceptionWithResponse ex) {
            throw ex;
        } else {
            return CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    protected void saveFailedLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, ActivityBillPayValidationEvent activityEvent, AutoLoanPrepareDataValidate prepareData, Exception e) {
        final MasterBillerResponse masterBiller = Optional.ofNullable(prepareData).map(AutoLoanPrepareDataValidate::getMasterBillerResponse).orElse(null);

        activityEvent = ActivityBillPayMapper.INSTANCE.updateActivityBillPayValidationFailed(activityEvent, masterBiller, cache, request, e);
        logEventPublisherService.saveActivityLog(activityEvent);
    }
}
