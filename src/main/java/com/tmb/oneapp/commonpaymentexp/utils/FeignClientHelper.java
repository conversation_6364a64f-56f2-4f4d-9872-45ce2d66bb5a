package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;


@Component
public class FeignClientHelper {
    private static final TMBLogger<FeignClientHelper> logger = new TMBLogger<>(FeignClientHelper.class);
    private static final String LOG_EXCEPTION_ERROR_MESSAGE_PATTERN = "Exception encountered while fetching data from {} in method {}.";
    private static final String LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN = "FeignException encountered while fetching data from {} in method {}.";

    public <T> T executeRequestSafely(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) {
        try {
            return executeRequest(supplier);
        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error("Method name {} : execute request without throw then return null. [result = null]", callerMethodName);
            return null;
        }
    }

    public <T> T executeRequestOrElse(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, T elseAction) {
        try {
            return executeRequest(supplier);

        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error("Method name {} : execute request without throw then return else value. [result = {}]", callerMethodName, elseAction.toString());
            return elseAction;
        }
    }

    public <T, X extends TMBCommonException> T executeRequestOrElseThrow(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, Supplier<? extends X> exceptionSupplier) throws X {
        try {
            return executeRequest(supplier);

        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            X customException = exceptionSupplier.get();
            logger.error("Method name {} : execute request throw custom TMBCommonException. [error-code = {}]", callerMethodName, customException.getErrorCode());
            throw customException;
        }
    }

    public <T> T executeRequest(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        try {
            ResponseEntity<TmbServiceResponse<T>> responseEntity = supplier.get();

            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Non-successful HTTP status code received");
            }

            TmbServiceResponse<T> tmbServiceResponse = responseEntity.getBody();

            if (tmbServiceResponse == null
                    || !(ResponseCode.SUCCESS.getCode().equals(tmbServiceResponse.getStatus().getCode())
                    || ResponseCode.SUCCESS_V2.getCode().equals(tmbServiceResponse.getStatus().getCode()))) {
                throw CommonServiceUtils.getBusinessTmbCommonException(tmbServiceResponse.getStatus().getCode(), tmbServiceResponse.getStatus().getMessage());
            }

            T data = tmbServiceResponse.getData();

            if (data == null) {
                throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Null data received");
            }

            return data;
        } catch (TMBCommonException e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw e;
        } catch (FeignException e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "FeignException occurred while fetching data");
        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error(LOG_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Exception occurred while fetching data");
        }
    }

    public void executeVoidRequest(Supplier<ResponseEntity<TmbOneServiceResponse<String>>> supplier) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<String>> responseEntity = supplier.get();

            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Non-successful HTTP status code received");
            }

            TmbOneServiceResponse<String> tmbServiceResponse = responseEntity.getBody();

            if (tmbServiceResponse == null
                    || !(ResponseCode.SUCCESS.getCode().equals(tmbServiceResponse.getStatus().getCode())
                    || ResponseCode.SUCCESS_V2.getCode().equals(tmbServiceResponse.getStatus().getCode()))) {
                throw CommonServiceUtils.getBusinessTmbCommonException(tmbServiceResponse.getStatus().getCode(), tmbServiceResponse.getStatus().getMessage());
            }

        } catch (TMBCommonException e) {
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), getCallerMethodName(new Throwable().getStackTrace()), e);
            throw e;
        } catch (FeignException e) {
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), getCallerMethodName(new Throwable().getStackTrace()), e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "FeignException occurred while fetching data.");
        } catch (Exception e) {
            logger.error(LOG_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), getCallerMethodName(new Throwable().getStackTrace()), e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Exception occurred while fetching data.");
        }
    }

    private static String getCallerMethodName(StackTraceElement[] stackTrace) {
        if (stackTrace == null || stackTrace.length < 3) {
            return StringUtils.EMPTY;
        }

        try {
            return StringUtils.joinWith(".", stackTrace[2].getMethodName(), stackTrace[1].getMethodName());
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }
}
