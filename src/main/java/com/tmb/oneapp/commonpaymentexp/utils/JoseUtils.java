package com.tmb.oneapp.commonpaymentexp.utils;

import com.nimbusds.jose.EncryptionMethod;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWEAlgorithm;
import com.nimbusds.jose.JWEHeader;
import com.nimbusds.jose.JWEObject;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.Payload;
import com.nimbusds.jose.crypto.RSADecrypter;
import com.nimbusds.jose.crypto.RSAEncrypter;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import lombok.experimental.UtilityClass;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.text.ParseException;
import java.util.Base64;
import java.util.Map;

@UtilityClass
public class JoseUtils {

    private static final TMBLogger<JoseUtils> logger = new TMBLogger<>(JoseUtils.class);

    private static final String JWS_VERIFICATION_FAILED_MESSAGE = "JWS verification failed";
    private static final JWSAlgorithm SIGNATURE_ALGORITHM = JWSAlgorithm.RS256;
    private static final JWEAlgorithm KEY_ENCRYPTION_ALGORITHM = JWEAlgorithm.RSA_OAEP_256;
    private static final EncryptionMethod CONTENT_ENCRYPTION_ALGORITHM = EncryptionMethod.A256GCM;

    /**
     * Encrypts a plaintext using JWE with a given RSA public key.
     *
     * @param plainText    The text to encrypt.
     * @param rsaPublicKey The RSA public key to use for encryption.
     * @return The JWE Compact Serialization string.
     * @throws TMBCommonException if encryption fails.
     */
    public String encrypt(String plainText, RSAKey rsaPublicKey) throws TMBCommonException {
        try {
            JWEHeader header = new JWEHeader.Builder(KEY_ENCRYPTION_ALGORITHM, CONTENT_ENCRYPTION_ALGORITHM)
                    .contentType("application/json")
                    .keyID(rsaPublicKey.getKeyID())
                    .build();

            JWEObject jweObject = new JWEObject(header, new Payload(plainText));
            RSAEncrypter encrypter = new RSAEncrypter(rsaPublicKey);
            jweObject.encrypt(encrypter);
            return jweObject.serialize();

        } catch (JOSEException e) {
            logger.error("Error during JWE encryption with JWK", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWE_ENCRYPTION_FAILED, "Encryption failed");
        }
    }

    /**
     * Decrypts a JWE string using the appropriate key from a JWKSet.
     *
     * @param jweString The JWE Compact Serialization string.
     * @param jwkSet    The JWKSet containing the decryption key.
     * @return The decrypted plaintext.
     * @throws TMBCommonException if decryption fails.
     */
    public String decrypt(String jweString, JWKSet jwkSet) throws TMBCommonException {
        try {
            JWEObject jweObject = JWEObject.parse(jweString);

            String keyId = jweObject.getHeader().getKeyID();
            if (keyId == null) {
                logger.error("JWE header does not contain a Key ID (kid)");
                throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWE_DECRYPTION_FAILED, "Key ID difference between partner and os");
            }

            JWK key = jwkSet.getKeyByKeyId(keyId);
            if (key == null) {
                logger.error("Private key with kid '{}' not found in JWKSet", keyId);
                throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWK_KEY_NOT_FOUND, "Cannot decrypt because Partner sent incorrect key id " + keyId);
            }
            if (!(key instanceof RSAKey)) {
                logger.error("Key with kid '{}' is not an RSA key", keyId);
                throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWK_INCORRECT_ALGORITHM, "Cannot decrypt because Incorrect algorithm");
            }

            RSADecrypter decrypter = new RSADecrypter(key.toRSAKey());
            jweObject.decrypt(decrypter);

            return jweObject.getPayload().toString();

        } catch (ParseException | JOSEException e) {
            logger.error("Error during JWE decryption with JWKSet", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWE_DECRYPTION_FAILED, "Decryption failed");
        }
    }

    /**
     * Generates a JWS for a given payload using a private RSA key.
     *
     * @param rsaPrivateKey The RSA private key for signing.
     * @param claims        The claims to include in the JWS payload.
     * @return The JWS Compact Serialization string.
     * @throws TMBCommonException if JWS generation fails.
     */
    public String generateJws(RSAKey rsaPrivateKey, Map<String, Object> claims) throws TMBCommonException {
        try {
            JWSHeader header = new JWSHeader.Builder(SIGNATURE_ALGORITHM)
                    .keyID(rsaPrivateKey.getKeyID())
                    .build();

            JWSObject jwsObject = new JWSObject(header, new Payload(claims));
            RSASSASigner signer = new RSASSASigner(rsaPrivateKey);
            jwsObject.sign(signer);
            return jwsObject.serialize();

        } catch (JOSEException e) {
            logger.error("Error during JWS generation with JWK", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWS_GENERATION_FAILED, "JWS generation failed");
        }
    }

    /**
     * Verifies a JWS signature using a public RSA key.
     *
     * @param jwsString    The JWS Compact Serialization string.
     * @param rsaPublicKey The RSA public key for verification.
     * @return The payload if the signature is valid.
     * @throws TMBCommonException if verification fails.
     */
    public Payload verifyJws(String jwsString, RSAKey rsaPublicKey) throws TMBCommonException {
        try {
            JWSObject jwsObject = JWSObject.parse(jwsString);
            RSASSAVerifier verifier = new RSASSAVerifier(rsaPublicKey);

            if (jwsObject.verify(verifier)) {
                return jwsObject.getPayload();
            } else {
                throw new JOSEException("JWS signature verification failed.");
            }
        } catch (ParseException | JOSEException e) {
            logger.error(JWS_VERIFICATION_FAILED_MESSAGE, e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWS_VERIFICATION_FAILED, JWS_VERIFICATION_FAILED_MESSAGE);
        }
    }

    /**
     * @deprecated Use {@link #encrypt(String, RSAKey)} instead.
     */
    @Deprecated
    public String encrypt(String plainText, String publicKeyStr) throws TMBCommonException {
        try {
            RSAPublicKey rsaPublicKey = (RSAPublicKey) loadPublicKey(publicKeyStr);
            JWEHeader header = new JWEHeader.Builder(KEY_ENCRYPTION_ALGORITHM, CONTENT_ENCRYPTION_ALGORITHM)
                    .contentType("application/json")
                    .build();
            JWEObject jweObject = new JWEObject(header, new Payload(plainText));
            jweObject.encrypt(new RSAEncrypter(rsaPublicKey));
            return jweObject.serialize();
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | JOSEException | IllegalArgumentException e) {
            logger.error("Error during JWE encryption with string key", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWE_ENCRYPTION_FAILED, "Encryption failed");
        }
    }

    /**
     * @deprecated Use {@link #decrypt(String, JWKSet)} instead.
     */
    @Deprecated
    public String decrypt(String jweString, String privateKeyStr) throws TMBCommonException {
        try {
            PrivateKey rsaPrivateKey = loadPrivateKey(privateKeyStr);
            JWEObject jweObject = JWEObject.parse(jweString);
            jweObject.decrypt(new RSADecrypter(rsaPrivateKey));
            return jweObject.getPayload().toString();
        } catch (ParseException | JOSEException | NoSuchAlgorithmException | InvalidKeySpecException | IllegalArgumentException e) {
            logger.error("Error during JWE decryption with string key", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWE_DECRYPTION_FAILED, "Decryption failed");
        }
    }

    /**
     * Generates a JWS for a given payload using a private key string.
     */
    public String generateJws(String privateKeyStr, Map<String, Object> claims) throws TMBCommonException {
        try {
            PrivateKey rsaPrivateKey = loadPrivateKey(privateKeyStr);
            JWSHeader header = new JWSHeader.Builder(SIGNATURE_ALGORITHM).build();
            JWSObject jwsObject = new JWSObject(header, new Payload(claims));
            jwsObject.sign(new RSASSASigner(rsaPrivateKey));
            return jwsObject.serialize();
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | JOSEException | IllegalArgumentException e) {
            logger.error("Error during JWS generation with string key", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWS_GENERATION_FAILED, "JWS generation failed");
        }
    }

    /**
     * @deprecated Use {@link #verifyJws(String, RSAKey)} instead.
     */
    @Deprecated
    public Payload verifyJws(String jwsString, String publicKeyStr) throws TMBCommonException {
        try {
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            JWSObject jwsObject = JWSObject.parse(jwsString);
            JWSVerifier verifier = new RSASSAVerifier((RSAPublicKey) publicKey);
            if (jwsObject.verify(verifier)) {
                return jwsObject.getPayload();
            } else {
                throw new JOSEException("JWS signature verification failed.");
            }
        } catch (ParseException | JOSEException | NoSuchAlgorithmException | InvalidKeySpecException | IllegalArgumentException e) {
            logger.error("JWS verification failed with string key", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWS_VERIFICATION_FAILED, JWS_VERIFICATION_FAILED_MESSAGE);
        }
    }

    private PrivateKey loadPrivateKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] keyBytes = Base64.getDecoder().decode(key);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePrivate(spec);
    }

    private PublicKey loadPublicKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] keyBytes = Base64.getDecoder().decode(key);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePublic(spec);
    }
}
