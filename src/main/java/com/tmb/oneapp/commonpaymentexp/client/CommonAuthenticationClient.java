package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthForceFR;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRefResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

@FeignClient(name = "${feign.oauth.service.name}", url = "${feign.oauth.service.endpoint}")
public interface CommonAuthenticationClient {

    @PostMapping(value = "/v1/oneapp-auth-service/oauth/common-authen/verify-ref", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CommonAuthenVerifyRefResponse>> verifyCommonAuthentication(
            @RequestHeader(HEADER_CORRELATION_ID) final String correlationId,
            @RequestHeader(CommonPaymentExpConstant.HEADER_CRM_ID_FOR_COMMON_AUTHEN) final String crmId,
            @RequestHeader(CommonPaymentExpConstant.HEADER_IP_ADDRESS) final String ipAddress,
            @Valid @RequestBody CommonAuthenVerifyRefRequest request);

    @GetMapping(value = "/v1/oneapp-auth/common-authen/force-fr", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CommonAuthForceFR>> getCommonAuthForceFR(
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(HEADER_CRM_ID) String crmId);

    @GetMapping(value = "/apis/oauth/cache/{key}", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<String>> getCacheData(
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @PathVariable("key") String key);
}
