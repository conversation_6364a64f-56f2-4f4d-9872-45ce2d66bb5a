#================= START Mandatory config =================
server.port=8080
spring.application.name=common-payment-exp
spring.application.description=
oneapp.ocp.domain=dev3-oneapp.svc

#Common utility
utility.common.service.endpoint=http://common-service.${oneapp.ocp.domain}

private.key.location=keys/rsa_private.key
public.key.location=keys/rsa_public.key
#================= END Mandatory config =================

#================= START Kafka config =================
kafka.prefix.topic=
spring.kafka.jaas.options.username=appusr
spring.kafka.jaas.options.password=
spring.kafka.producer.bootstrap-servers=stmoneamqd1.tmbbank.local:9092

#ActivityConfig
com.tmb.oneapp.common.payment.exp.activity=${kafka.prefix.topic}activity
com.tmb.oneapp.common.payment.exp.financial=${kafka.prefix.topic}financial_log
com.tmb.oneapp.common.payment.exp.transaction=${kafka.prefix.topic}transaction_log
com.tmb.oneapp.common.payment.exp.payment.status=${kafka.prefix.topic}payment_status

#================= END Kafka config =================

#================= START Feign group =================
#Customer account biz
feign.customers.account.biz.service.name=customer-account-biz
feign.customers.account.biz.service.url=http://customer-account-biz.${oneapp.ocp.domain}

#Customer exp
feign.customer.exp.service.name=customer-exp
feign.customer.exp.service.url=http://customers-exp-service.${oneapp.ocp.domain}

#Customer service
feign.customers.service.name=customer-service
feign.customers.service.url=http://customers-service.${oneapp.ocp.domain}

#Payment service
feign.payment.service.name=payment-service
feign.payment.service.url=http://payment-service.${oneapp.ocp.domain}

#Retail service
feign.retail.lending.biz.name=retail-lending-biz
feign.retail.lending.biz.url=http://retail-lending-biz.${oneapp.ocp.domain}

#Common service
feign.common.service.name=common-service
feign.common.service.url=http://common-service.${oneapp.ocp.domain}

#Credit-card service
feign.credit.card.service.name=creditcard-service
feign.credit.card.service.url=http://creditcard-service.${oneapp.ocp.domain}

#Customer transaction service
feign.customers.transaction.service.name=customers-transaction-service
feign.customers.transaction.service.endpoint=http://customers-transaction-service.${oneapp.ocp.domain}

#oauth service
feign.oauth.service.name=oneapp-auth-service
feign.oauth.service.endpoint=http://oneapp-auth-service.${oneapp.ocp.domain}

#notification service
feign.notification.service.name=notification-service
feign.notification.service.endpoint=http://notification-service.${oneapp.ocp.domain}
notification-service.e-noti.default.channel.th=\u0E17\u0E35\u0E17\u0E35\u0E1A\u0E35 \u0E17\u0E31\u0E0A
notification-service.e-noti.default.channel.en=ttb touch

#HP Exp
feign.hp.exp.service.name=hp-exp-service
feign.hp.exp.service.url=http://hp-exp-service.${oneapp.ocp.domain}

#Transfer service
feign.transfer.service.name=transfer-service
feign.transfer.service.url=http://transfer-service.${oneapp.ocp.domain}

#Bank service
feign.bank.service.name=bank-service
feign.bank.service.url=http://bank-service.${oneapp.ocp.domain}

#Account service
feign.account.service.name=account-service
feign.account.service.url=http://accounts-service.${oneapp.ocp.domain}

#Loyalty Biz
feign.loyalty.biz.name=loyalty-biz
feign.loyalty.biz.url=http://loyalty-biz.${oneapp.ocp.domain}
#================= END Feign group =================

#================= START DB config =================
#Redis
spring.redis.mode=standalone
spring.redis.host=***********
spring.redis.port=6379
spring.redis.pool.min-idle=0
spring.redis.pool.max-idle=8
spring.redis.pool.max-total=8
spring.redis.read-from=replicaPreferred
spring.redis.adaptive-refresh-trigger-timeout=15
spring.redis.periodic-refresh=15
#================= END DB config =================

#================= START Feature config =================
#Prefix-deeplink on Initial common-payment
common.payment.initial.deeplink.url=oneappvit://linkaccess/commonpayment
#Deeplink manager URL for external partners
common.payment.deeplink.manager.url=https://www.ttbbank.com/th/deeplink-suite/redirect
#Universal link URL for non-webview access
common.payment.universal.link.url=https://touch.ttbdirect.com/linkaccess/commonpayment
common.payment.initial.cache.expire.second=600
common.payment.initial.enable.check.toggle.allow.common.payment=true

#FR payment accumulate usage limit
fr.payment.accumulate.usage.limit=200000

#FR payment accumulate usage limit
amlo.amount.validate.for.get.tax.id=700000

#Bill Prompt pay transaction validate ISO
qr.payment.iso20022.flag=false
#================= END Feature config =================

#======= REDIS CONFIG ===================================
#Redis Library Feature Configuration
redis.cache.enabled=true
redis.cache.custom.ttl=true
redis.template.enabled=true

#======= END REDIS CONFIG ===================================

#================= START Other config =================
#Swagger
springdoc.api-docs.path=/v1/common-payment-exp/api-docs
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
springdoc.swagger-ui.use-root-path=false
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.urls[0].name=common-payment-exp
springdoc.swagger-ui.urls[0].url=/v1/common-payment-exp/api-docs
swagger.host=https://apis-portal.oneapp.tmbbank.local, http://localhost:${server.port}

#Add Inbound/OutBound Log
app.api.logging.enable=true
app.api.logging.max-length=10000
app.api.logging.url-patterns=*
app.api.logging.feign.enable=true
app.api.logging.feign.exclude-url-patterns=/actuator/**
#================= END Other config =================

#================= START JWK Set config =================
jwk.sets.client.shopee={"keys":[{"p":"8JO4BI--_ZvaBKfD0eY1CeAtQMvnNpY4hWJUOxtFOrgYigK7vskdJFhiGg3_awSpnX4BrB0jsxwhs81_tHHlTMdQ_kzdwFRXBa9SnbCmfqsAdpD_9TYSwWtbHUxLgo7DimD0iJVe1rC12WL9jIhxwIBa2WaOQ2E34jdlRb7slCE","kty":"RSA","q":"3mHgwxwWO809WoFKppyns1qmyWQijlqyCdbmTJ7EFDZXp8WZS6aKJH1fTZ_kvSBpslFNxFyCf52khLzkQZgEDs1NeStifE-elnEveRYrByPNflASBBrW_nuX0PxFILpNCy_jD-o43K7tzB7scicUeUBbWfTne2YHDipDu9Pi2gM","d":"YY1KJM4LrimMEDKUs6Lwru0xz-goLVnyes2oHv-3sntHnImI2RwgJIEsGiBfDMuCe0WzIKxQl1DMUkRm9XxOJ1G4hhViFq7V0mf0D3KakWprwylnZLIllJlNrD8UH4AiqF4iJ7sb-MBf34Za8wApiq0GyVB5B96TxxvI3PqchrvsFc_qpHcsIV22Wj8mcZ27cKOYcUNwJsmHl_TD-16AAfucNBBZ9R0LFzbXCZKcS5i0Opxrrvm_5JKPR6wmcERdTJrYcBttSgan8EBbpeLTq8qBp5g45DCZFPgXrnulb9Q9TshUS7ZcKWzwoUhideFhnicmuTePWHp7gHCmUanh","e":"AQAB","use":"sig","kid":"signing-key-shopee","qi":"YvBclCfGOt43-zlvqn65fsuCMnBaOrmdNlzk0cLiLLEa6n6B3Xfgm0gsECUld9jfnBgINnHPmeJmUXcUQSPZ2lRJhSEh0nSvAoB8iCMtmtl9mUjIMPerweNGU7WM4Efx6Sy-KkrliEL9D0ta2ztNrlSB-_fxAbH8pcHyhrUvHKw","dp":"t1Zbrl49ltfm1hVjzdo6IUSOOAssMRVaDYxZ1JF7uLwDN2nN2h857jDM0j5IoC31dOhKLfgm9KBDrtq0r7b8n_5lYucBN0kO6G4r77BnsbBvseveo-iJco116mXyRZS2wAYTVONAWyxf6ULfJvH0fldS5MqbMA4uoF7G9tgBQwE","alg":"RS256","dq":"j0QFVg1JMZumtCDpY-82hvCW1NXDAWzimD4vPhtmfRMJY5eEi90t0BZJCKxC46j3Dx8b964VkHuR2sYKU0vJCXQwPii6GRX-ODOQ_vROIhppr7-L68MS0P30r3lmW5BKRDPEl6MwoNO9dSxS7bPJl9fjQk_ptXDkiLanzmDSG9s","n":"0PwUxUpQTZEW-hjx3AwWG5BXRdc7F5UnRRbJNePOE7T4s47F6dDQVwpQkCvb9aUU8ZuzvD8SxnA7NKATw8a5moJoomR8nqs3tYmxvE6LR860VMve6bhR_3SBHhkstTDBJGzOxvB_5f6ZcEDB0aH6vCwP_HIYTdq958MUSnj7ug17Bip1GNXmEVdNa2D0GWQUHI-QQN-9Qs4G70pLnFVR4YxL43u4AUB1MEg4BHc37dH4rKNO1HkRPSRg7W6FztJxJSfhmmzvTAahaBNTnZEzdALSfJm-X7h0byBKuTICcbb8r_O9FaPRGHw7lavmkhmz25A3YIiPyGYz0fZzqwvWYw"},{"p":"8JO4BI--_ZvaBKfD0eY1CeAtQMvnNpY4hWJUOxtFOrgYigK7vskdJFhiGg3_awSpnX4BrB0jsxwhs81_tHHlTMdQ_kzdwFRXBa9SnbCmfqsAdpD_9TYSwWtbHUxLgo7DimD0iJVe1rC12WL9jIhxwIBa2WaOQ2E34jdlRb7slCE","kty":"RSA","q":"3mHgwxwWO809WoFKppyns1qmyWQijlqyCdbmTJ7EFDZXp8WZS6aKJH1fTZ_kvSBpslFNxFyCf52khLzkQZgEDs1NeStifE-elnEveRYrByPNflASBBrW_nuX0PxFILpNCy_jD-o43K7tzB7scicUeUBbWfTne2YHDipDu9Pi2gM","d":"YY1KJM4LrimMEDKUs6Lwru0xz-goLVnyes2oHv-3sntHnImI2RwgJIEsGiBfDMuCe0WzIKxQl1DMUkRm9XxOJ1G4hhViFq7V0mf0D3KakWprwylnZLIllJlNrD8UH4AiqF4iJ7sb-MBf34Za8wApiq0GyVB5B96TxxvI3PqchrvsFc_qpHcsIV22Wj8mcZ27cKOYcUNwJsmHl_TD-16AAfucNBBZ9R0LFzbXCZKcS5i0Opxrrvm_5JKPR6wmcERdTJrYcBttSgan8EBbpeLTq8qBp5g45DCZFPgXrnulb9Q9TshUS7ZcKWzwoUhideFhnicmuTePWHp7gHCmUanh","e":"AQAB","use":"enc","kid":"encryption-key-shopee","qi":"YvBclCfGOt43-zlvqn65fsuCMnBaOrmdNlzk0cLiLLEa6n6B3Xfgm0gsECUld9jfnBgINnHPmeJmUXcUQSPZ2lRJhSEh0nSvAoB8iCMtmtl9mUjIMPerweNGU7WM4Efx6Sy-KkrliEL9D0ta2ztNrlSB-_fxAbH8pcHyhrUvHKw","dp":"t1Zbrl49ltfm1hVjzdo6IUSOOAssMRVaDYxZ1JF7uLwDN2nN2h857jDM0j5IoC31dOhKLfgm9KBDrtq0r7b8n_5lYucBN0kO6G4r77BnsbBvseveo-iJco116mXyRZS2wAYTVONAWyxf6ULfJvH0fldS5MqbMA4uoF7G9tgBQwE","alg":"RSA-OAEP-256","dq":"j0QFVg1JMZumtCDpY-82hvCW1NXDAWzimD4vPhtmfRMJY5eEi90t0BZJCKxC46j3Dx8b964VkHuR2sYKU0vJCXQwPii6GRX-ODOQ_vROIhppr7-L68MS0P30r3lmW5BKRDPEl6MwoNO9dSxS7bPJl9fjQk_ptXDkiLanzmDSG9s","n":"0PwUxUpQTZEW-hjx3AwWG5BXRdc7F5UnRRbJNePOE7T4s47F6dDQVwpQkCvb9aUU8ZuzvD8SxnA7NKATw8a5moJoomR8nqs3tYmxvE6LR860VMve6bhR_3SBHhkstTDBJGzOxvB_5f6ZcEDB0aH6vCwP_HIYTdq958MUSnj7ug17Bip1GNXmEVdNa2D0GWQUHI-QQN-9Qs4G70pLnFVR4YxL43u4AUB1MEg4BHc37dH4rKNO1HkRPSRg7W6FztJxJSfhmmzvTAahaBNTnZEzdALSfJm-X7h0byBKuTICcbb8r_O9FaPRGHw7lavmkhmz25A3YIiPyGYz0fZzqwvWYw"}]}
jwk.sets.client.lazada={"keys":[{"p":"wSbR-DYgGhvTar0KORpzP4ZNUzuRCc4L_U3xd9niSLtMWW_2upMZ56XHD58u51GGtQ_ISm4ShuD3kMf2OavFYye2KqqugsCoT9FI6_jqzNAbdYdXn17eM24B4YnJc23jw8jsK_acvI4dlQ4pUH2qERKKTi0hhDfbJJRXi3Hi0us","kty":"RSA","q":"5ie60G1LI9003NThKpHJ7HHYxUr0i8G6bV76ff9U8IaHmI9HbPj3cGZzVD3kg5tqLT020rQL0dbUQWZC-D4F0qboZV_WcIWlq0FuPHhOcV5Khn8n8A5-IUdUVlFR-o5dUWiEmeQbXUxNhrsHRzR_Cm0ASFv6YmF99cuKRNRuVS8","d":"NLJdvRjWuqMicEyn78N7ASUQpFqKonYiKddxyUOSjWK6h4CIKUDsSDAEsRVS4p_TmpQJZzlFlqgKl7FiWsuphZy5CNT6T8lvoHzhsAONp8CdjduDrDw7CMLm96oId8XREP2-octmM9MPv3rXIBvawLCLwSBN4ZCLAFwGMSDos91MQ7sOTmFHDfHJ50VA-_ykmJUQDICvoxB-TyJluc3-iiI7wiVtEB-IsJb6PCJYrNsOA0Ic-rYV8L_Sws1kEIPxU96KWkytfU9jvY4ugLzmakUhLu8fv__jIAny4v1V4MylAI65lt3t8GA9rop6jJw0-YOrxr5hTfYChD7aJVMkQQ","e":"AQAB","use":"sig","kid":"signing-key-lazada","qi":"INTzlHkCC9tfCp_e1y_j1y0CO8qqPvXtZbdOtnsJucS6z4UP5Zxv7TjfZaSKoM7cySIeMLsPyqc85WiH8oAfPLk_sPivIGri_MTvu9nAU-GqJvkpltVXEQRFBwTNIuWXObRG3p4Mg4rvgaJu92dFC3B9d9aCg1wmf3na2rbymiM","dp":"nGce5uqNHBckHu5sdMjiQbIysw8s5s-eDcMroG0iOtrPfCyZkYLEGzu18QRpKo5tkffxq7Nt9SH91ZLV4uUgtKXXcL09CkuO_vLtJYcuOiLZ2VV6smZTbznI9CRqp7l4EXWFKCRNzrEVazlIYxEewSxOKfeJiFLidMBaE2Ro03k","alg":"RS256","dq":"1Cn_vq9huxfp6UCcqN9oIvz39TeZo1PdBYGurIO5y1hYmzxJfZ0sjIMFAb1kcCXOhRGO_kFXPAMr-3XHeovCVmPii8EDLwsko3BXrIrC1SM01DNXn6Xcg9T7W-7vAWhHggo1Yp-y0hjKxs6WcP9ltTiyMAIKGLMCqJkPR1AEvaU","n":"rabagnQAQC2vGsMk0c61tduRuvGR-3qaDYK5XfvTu3xKr9IRbRD1nRhDJ6wFqb2Op1UcJ5PVXeJb2g2ELEEjB0nreeYXIMt5byt78aaWFF37p8NDck2IMwzD_2Uf8i3V3gCc7jYQDesJ6yEJnWL_VnCcVxFJjKVWGaDvEz68gj8HgBKt_GKpeGzixPwtheatyYWQNFsbr35ZyFKWcXaPCdGMuXCaVab6wKOQzSxmYmHi5ZwNpVmrZKpLuhFUExZoKQf85fM8T4hHcUXVOSUmy3vByvtSP22r015JvALUUfaaM_W-TlsSasSsWXZSYYOKlKjuZ-ASa4O909n3dabAJQ"},{"p":"wSbR-DYgGhvTar0KORpzP4ZNUzuRCc4L_U3xd9niSLtMWW_2upMZ56XHD58u51GGtQ_ISm4ShuD3kMf2OavFYye2KqqugsCoT9FI6_jqzNAbdYdXn17eM24B4YnJc23jw8jsK_acvI4dlQ4pUH2qERKKTi0hhDfbJJRXi3Hi0us","kty":"RSA","q":"5ie60G1LI9003NThKpHJ7HHYxUr0i8G6bV76ff9U8IaHmI9HbPj3cGZzVD3kg5tqLT020rQL0dbUQWZC-D4F0qboZV_WcIWlq0FuPHhOcV5Khn8n8A5-IUdUVlFR-o5dUWiEmeQbXUxNhrsHRzR_Cm0ASFv6YmF99cuKRNRuVS8","d":"NLJdvRjWuqMicEyn78N7ASUQpFqKonYiKddxyUOSjWK6h4CIKUDsSDAEsRVS4p_TmpQJZzlFlqgKl7FiWsuphZy5CNT6T8lvoHzhsAONp8CdjduDrDw7CMLm96oId8XREP2-octmM9MPv3rXIBvawLCLwSBN4ZCLAFwGMSDos91MQ7sOTmFHDfHJ50VA-_ykmJUQDICvoxB-TyJluc3-iiI7wiVtEB-IsJb6PCJYrNsOA0Ic-rYV8L_Sws1kEIPxU96KWkytfU9jvY4ugLzmakUhLu8fv__jIAny4v1V4MylAI65lt3t8GA9rop6jJw0-YOrxr5hTfYChD7aJVMkQQ","e":"AQAB","use":"enc","kid":"encryption-key-lazada","qi":"INTzlHkCC9tfCp_e1y_j1y0CO8qqPvXtZbdOtnsJucS6z4UP5Zxv7TjfZaSKoM7cySIeMLsPyqc85WiH8oAfPLk_sPivIGri_MTvu9nAU-GqJvkpltVXEQRFBwTNIuWXObRG3p4Mg4rvgaJu92dFC3B9d9aCg1wmf3na2rbymiM","dp":"nGce5uqNHBckHu5sdMjiQbIysw8s5s-eDcMroG0iOtrPfCyZkYLEGzu18QRpKo5tkffxq7Nt9SH91ZLV4uUgtKXXcL09CkuO_vLtJYcuOiLZ2VV6smZTbznI9CRqp7l4EXWFKCRNzrEVazlIYxEewSxOKfeJiFLidMBaE2Ro03k","alg":"RSA-OAEP-256","dq":"1Cn_vq9huxfp6UCcqN9oIvz39TeZo1PdBYGurIO5y1hYmzxJfZ0sjIMFAb1kcCXOhRGO_kFXPAMr-3XHeovCVmPii8EDLwsko3BXrIrC1SM01DNXn6Xcg9T7W-7vAWhHggo1Yp-y0hjKxs6WcP9ltTiyMAIKGLMCqJkPR1AEvaU","n":"rabagnQAQC2vGsMk0c61tduRuvGR-3qaDYK5XfvTu3xKr9IRbRD1nRhDJ6wFqb2Op1UcJ5PVXeJb2g2ELEEjB0nreeYXIMt5byt78aaWFF37p8NDck2IMwzD_2Uf8i3V3gCc7jYQDesJ6yEJnWL_VnCcVxFJjKVWGaDvEz68gj8HgBKt_GKpeGzixPwtheatyYWQNFsbr35ZyFKWcXaPCdGMuXCaVab6wKOQzSxmYmHi5ZwNpVmrZKpLuhFUExZoKQf85fM8T4hHcUXVOSUmy3vByvtSP22r015JvALUUfaaM_W-TlsSasSsWXZSYYOKlKjuZ-ASa4O909n3dabAJQ"}]}
jwk.sets.client.lineman={"keys":[{"p":"6gBxQr35F7Ynh92G70Gzo_3J4nxA28GOJlpZPirMd529TUavC0ivvDzeI9GmPpR1Dkp2QFhqByJNsdChNqnEt-caCGrrQ8f28gE_SrQ8VI3YhROLVNsUZ7CLTnwm9IMdoHEgOx6IzgKOJa_syujo-GCvfdzl1eYLmKRjBv7clPU","kty":"RSA","q":"zQK3dxqS0MUvp6ABLQTIlOufn3aRJ7s82MBf1HP7g2dmCWU8W7-N59okPXNWqLMRNpBMEokbVhMCiZQ53BKdbzD3YZyXZzXvzd1TdXTbccv7qxCVq3Ag7Hm88ZHJjlHf3YpltdF7Y2oVHwMk4Z_MbShb0BQnb1Qqnx3055uITw8","d":"OGQQVrozdqLoDZEbNLOJrs_kX-d8wwhE7CvFQDEvJd7cojgbwG0F5-ZW7SMHxe8aFhB4nIMIbs_QomKC8oeuuVKzzmh7fp5I5-uUQ19Cjdh5mJIoSMh4IWmPZbS68uoBVCSwL1-GKBH8tVL0DoJpjwZIU29O3ZcJVyPxgUllAsrZgZfnCsXGR5-2DV8m_Ep9lXQJgJHd4ky-I6tNKX9gHX3OSC60ihEE0LmxTwly1TEL9LwVMx5HqtGos8zrAf4UtFC8kpnJJx0o8onqfEwo6M5lujJcInyBYbuYYOwYQUxKjrpigmNKz9mg3r4ode0nq1v2ix1x1jP8jmKvfQksxQ","e":"AQAB","use":"sig","kid":"signing-key-lineman","qi":"o8udztJmsxMBzbCF1XpQ3KHsWGO4tl1RY488hkECXGoHDu2SppXqVrs33BTgryx_5KwJQfHJOG27Atuexiy0ZdgMWMA7Z9-XunqIj4s50IVUEemFOdmAb1UHzjU3RFztf20_Gnstr27NO7SkqehaMBkg_euQMUlezKG8KjKZOc0","dp":"0eArtYzmCRpbQkSybY0kLgXGONjw4ykq8cM_H78LPI3B57lp7lj3JYICS2YSETbd9j9a6ENkdZmSzdUtgvWpdrhUZRBgip6jIlN0DPCy27ONa24oHaeRGZ8CkxZPaibfP7O3EM9GLF-mVq7_Vs6nhX5SaDTj_4EhDXsDRpb4CLE","alg":"RS256","dq":"uo68UYA3FhA6m3l5WHQ_63S3ny8QTG2Qo1X8XqLkv-E581bSpFMeWVwjGmB4_a9l_Jc3Aq8N1zgZWQUv2dp-tY6Dr6oGBQAKJvY_WmVqxJVxgWvoJ0g0Tb1CeTnHhuFurHWitN_aPs9sRPphd4gpSTQuyJ1ufVrEJmPmV1Urp3U","n":"u2TWZoQbjlhlGavoX13FgBGx-FOazwshYr9DkiLy2D8eddynLFHjDUn7R_v2OZti7AlNkPiRW0Oxppcm-arFio4-rCrjHNV5sNYvU8PfRuG9uImot3Qu4oyUQbXSzzLAXR_Joyu7iMK4sbpm8YNYv6VVfnx58_CJ_NzCVoChU8oIBJybYDZnkY6YnbhaASyXGXLxrN8x6lHaUNbFQJfhvqvFFfI6C16x55I7r8pZIqupdYQjelLcE7Q3cNhU7hnR61_f-hR6Qr-17cJK_7zx1FoKINHoPrQDRAQq5wx-TorouuKA53jzeb22iFY1YNVMTrKHHco0Z__F_NaBegxVWw"},{"p":"6gBxQr35F7Ynh92G70Gzo_3J4nxA28GOJlpZPirMd529TUavC0ivvDzeI9GmPpR1Dkp2QFhqByJNsdChNqnEt-caCGrrQ8f28gE_SrQ8VI3YhROLVNsUZ7CLTnwm9IMdoHEgOx6IzgKOJa_syujo-GCvfdzl1eYLmKRjBv7clPU","kty":"RSA","q":"zQK3dxqS0MUvp6ABLQTIlOufn3aRJ7s82MBf1HP7g2dmCWU8W7-N59okPXNWqLMRNpBMEokbVhMCiZQ53BKdbzD3YZyXZzXvzd1TdXTbccv7qxCVq3Ag7Hm88ZHJjlHf3YpltdF7Y2oVHwMk4Z_MbShb0BQnb1Qqnx3055uITw8","d":"OGQQVrozdqLoDZEbNLOJrs_kX-d8wwhE7CvFQDEvJd7cojgbwG0F5-ZW7SMHxe8aFhB4nIMIbs_QomKC8oeuuVKzzmh7fp5I5-uUQ19Cjdh5mJIoSMh4IWmPZbS68uoBVCSwL1-GKBH8tVL0DoJpjwZIU29O3ZcJVyPxgUllAsrZgZfnCsXGR5-2DV8m_Ep9lXQJgJHd4ky-I6tNKX9gHX3OSC60ihEE0LmxTwly1TEL9LwVMx5HqtGos8zrAf4UtFC8kpnJJx0o8onqfEwo6M5lujJcInyBYbuYYOwYQUxKjrpigmNKz9mg3r4ode0nq1v2ix1x1jP8jmKvfQksxQ","e":"AQAB","use":"enc","kid":"encryption-key-lineman","qi":"o8udztJmsxMBzbCF1XpQ3KHsWGO4tl1RY488hkECXGoHDu2SppXqVrs33BTgryx_5KwJQfHJOG27Atuexiy0ZdgMWMA7Z9-XunqIj4s50IVUEemFOdmAb1UHzjU3RFztf20_Gnstr27NO7SkqehaMBkg_euQMUlezKG8KjKZOc0","dp":"0eArtYzmCRpbQkSybY0kLgXGONjw4ykq8cM_H78LPI3B57lp7lj3JYICS2YSETbd9j9a6ENkdZmSzdUtgvWpdrhUZRBgip6jIlN0DPCy27ONa24oHaeRGZ8CkxZPaibfP7O3EM9GLF-mVq7_Vs6nhX5SaDTj_4EhDXsDRpb4CLE","alg":"RSA-OAEP-256","dq":"uo68UYA3FhA6m3l5WHQ_63S3ny8QTG2Qo1X8XqLkv-E581bSpFMeWVwjGmB4_a9l_Jc3Aq8N1zgZWQUv2dp-tY6Dr6oGBQAKJvY_WmVqxJVxgWvoJ0g0Tb1CeTnHhuFurHWitN_aPs9sRPphd4gpSTQuyJ1ufVrEJmPmV1Urp3U","n":"u2TWZoQbjlhlGavoX13FgBGx-FOazwshYr9DkiLy2D8eddynLFHjDUn7R_v2OZti7AlNkPiRW0Oxppcm-arFio4-rCrjHNV5sNYvU8PfRuG9uImot3Qu4oyUQbXSzzLAXR_Joyu7iMK4sbpm8YNYv6VVfnx58_CJ_NzCVoChU8oIBJybYDZnkY6YnbhaASyXGXLxrN8x6lHaUNbFQJfhvqvFFfI6C16x55I7r8pZIqupdYQjelLcE7Q3cNhU7hnR61_f-hR6Qr-17cJK_7zx1FoKINHoPrQDRAQq5wx-TorouuKA53jzeb22iFY1YNVMTrKHHco0Z__F_NaBegxVWw"}]}
#================= END JWK Set config =================
